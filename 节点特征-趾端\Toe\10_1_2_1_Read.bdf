$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:03:38                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    1               20.     3.7021-90.      
GRID    2               10.     1.851-9 0.      
GRID    3               0.      0.      0.      
GRID    4               -10.    -1.851-90.      
GRID    5               -20.    -3.702-90.      
GRID    6               20.     10.     0.      
GRID    7               10.     10.     0.      
GRID    8               -1.851-910.     0.      
GRID    9               -10.    10.     0.      
GRID    10              -20.    10.     0.      
$==============================================================================$
CQUAD4  100001  1       1       2       7       6       
CQUAD4  100002  1       2       3       8       7       
CQUAD4  100003  1       3       4       9       8       
CQUAD4  100004  1       4       5       10      9       
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
