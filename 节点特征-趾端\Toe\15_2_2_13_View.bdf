$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:39:32                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16573           -600.   3015.   0.      
GRID    16583           -600.   3120.   0.      
GRID    16593           -600.   3120.   135.    
GRID    16649           -570.   3000.   0.      
GRID    16650           -585.   3000.   0.      
GRID    16651           -600.   3000.   0.      
GRID    16652           -615.   3000.   0.      
GRID    16653           -630.   3000.   0.      
GRID    16654           -570.   3015.   0.      
GRID    16655           -585.   3015.   0.      
GRID    16656           -615.   3015.   0.      
GRID    16657           -630.   3015.   0.      
GRID    16733           -600.   3030.   0.      
GRID    16783           -600.   3045.   0.      
GRID    16832           -600.   3060.   0.      
GRID    16882           -600.   3075.   0.      
GRID    16931           -600.   3090.   0.      
GRID    16981           -600.   3105.   0.      
GRID    49775           -600.   3120.   120.    
GRID    49776           -600.   3120.   105.    
GRID    49777           -600.   3120.   90.     
GRID    49778           -600.   3120.   75.     
GRID    49779           -600.   3120.   60.     
GRID    49780           -600.   3120.   45.     
GRID    49781           -600.   3120.   30.     
GRID    49782           -600.   3120.   15.     
GRID    71341           -600.   3105.   135.    
GRID    71342           -600.   3015.   15.     
GRID    71343           -600.   3104.206119.972 
GRID    71344           -600.   3029.95216.70561
GRID    71345           -600.   3044.19221.57155
GRID    71346           -600.   3057.2  29.13774
GRID    71347           -600.   3068.70538.83722
GRID    71348           -600.   3078.63350.14682
GRID    71349           -600.   3086.82362.77149
GRID    71350           -600.   3093.44376.28589
GRID    71351           -600.   3098.48690.46482
GRID    71352           -600.   3102.12 105.0683
GRID    71353           -600.   3105.41114.51462
GRID    71354           -600.   3090.84214.11553
GRID    71355           -600.   3076.04814.70329
GRID    71356           -600.   3091.69128.27512
GRID    71357           -600.   3104.30473.94908
GRID    71358           -600.   3063.26818.15918
GRID    71359           -600.   3100.74957.51653
GRID    71360           -600.   3046.23313.26835
GRID    71361           -600.   3088.22442.08906
GRID    71362           -600.   3080.05526.08506
GRID    71363           -600.   3104.72643.3857 
GRID    71364           -600.   3105.33529.43759
GRID    71365           -600.   3074.23232.55355
$==============================================================================$
CTRIA3  100860  1       71363   71361   71359   
CTRIA3  100861  1       71360   71344   71345   
CTRIA3  100862  1       71357   71350   71351   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100049  1       16649   16650   16655   16654   
CQUAD4  100050  1       16650   16651   16573   16655   
CQUAD4  100051  1       16651   16652   16656   16573   
CQUAD4  100052  1       16652   16653   16657   16656   
CQUAD4  100835  1       71353   71364   49781   49782   
CQUAD4  100836  1       16931   16882   71355   71354   
CQUAD4  100837  1       71357   71359   71349   71350   
CQUAD4  100838  1       49781   71364   71363   49780   
CQUAD4  100839  1       71344   16733   16573   71342   
CQUAD4  100840  1       16931   71354   71353   16981   
CQUAD4  100841  1       71343   71341   16593   49775   
CQUAD4  100842  1       71356   71364   71353   71354   
CQUAD4  100843  1       49776   49777   71351   71352   
CQUAD4  100844  1       71347   71348   71361   71365   
CQUAD4  100845  1       71356   71354   71355   71362   
CQUAD4  100846  1       16783   71360   71358   16832   
CQUAD4  100847  1       71362   71355   71358   71365   
CQUAD4  100848  1       71358   71346   71347   71365   
CQUAD4  100849  1       71348   71349   71359   71361   
CQUAD4  100850  1       16783   16733   71344   71360   
CQUAD4  100851  1       16981   71353   49782   16583   
CQUAD4  100852  1       49778   49779   71359   71357   
CQUAD4  100853  1       71363   71364   71356   71361   
CQUAD4  100854  1       49780   71363   71359   49779   
CQUAD4  100855  1       49775   49776   71352   71343   
CQUAD4  100856  1       49778   71357   71351   49777   
CQUAD4  100857  1       71361   71356   71362   71365   
CQUAD4  100858  1       71346   71358   71360   71345   
CQUAD4  100859  1       16832   71358   71355   16882   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
