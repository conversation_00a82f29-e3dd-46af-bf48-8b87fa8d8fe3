$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:04:48                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16572           2010.   200.    0.      
GRID    16582           2060.   200.    0.      
GRID    16592           2060.   200.    70.     
GRID    16640           2000.   180.    0.      
GRID    16641           2000.   190.    0.      
GRID    16642           2000.   200.    0.      
GRID    16643           2000.   210.    0.      
GRID    16644           2000.   220.    0.      
GRID    16645           2010.   180.    0.      
GRID    16646           2010.   190.    0.      
GRID    16647           2010.   210.    0.      
GRID    16648           2010.   220.    0.      
GRID    16728           2020.   200.    0.      
GRID    16778           2030.   200.    0.      
GRID    16827           2040.   200.    0.      
GRID    16877           2050.   200.    0.      
GRID    49749           2060.   200.    60.     
GRID    49750           2060.   200.    50.     
GRID    49751           2060.   200.    40.     
GRID    49752           2060.   200.    30.     
GRID    49753           2060.   200.    20.     
GRID    49754           2060.   200.    10.     
GRID    61601           2045.   200.    70.     
GRID    61602           2010.   200.    15.     
GRID    61603           2044.379200.    59.82797
GRID    61604           2019.922200.    17.3105 
GRID    61605           2028.27 200.    23.15039
GRID    61606           2034.699200.    31.05514
GRID    61607           2039.344200.    40.12539
GRID    61608           2042.522200.    49.80776
GRID    61609           2010.   200.    7.5     
GRID    61610           2052.5  200.    70.     
GRID    61611           2051.238200.    59.71303
GRID    61612           2040.41 200.    10.62908
GRID    61613           2050.5  200.    9.399915
GRID    61614           2051.879200.    18.74641
GRID    61615           2019.002200.    8.534843
GRID    61616           2049.773200.    49.28801
GRID    61617           2048.015200.    37.44856
GRID    61618           2028.2  200.    9.721301
GRID    61619           2040.317200.    26.4227 
GRID    61620           2034.373200.    17.28417
GRID    61621           2050.701200.    28.17791
GRID    61622           2044.807200.    18.48744
$==============================================================================$
CTRIA3  58457   1       61618   61615   61604   
CTRIA3  58458   1       61621   61619   61617   
CTRIA3  58459   1       61618   61620   61612   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  58436   1       61617   61616   49750   49751   
CQUAD4  58437   1       49749   61611   61610   16592   
CQUAD4  58438   1       61619   61621   61614   61622   
CQUAD4  58439   1       61607   61608   61616   61617   
CQUAD4  58440   1       61615   16728   16572   61609   
CQUAD4  58441   1       61603   61601   61610   61611   
CQUAD4  58442   1       61619   61622   61612   61620   
CQUAD4  58443   1       61614   49753   49754   61613   
CQUAD4  58444   1       61619   61606   61607   61617   
CQUAD4  58445   1       49753   61614   61621   49752   
CQUAD4  58446   1       61606   61619   61620   61605   
CQUAD4  58447   1       61614   61613   61612   61622   
CQUAD4  58448   1       61609   61602   61604   61615   
CQUAD4  58449   1       61618   61612   16827   16778   
CQUAD4  58450   1       61611   49749   49750   61616   
CQUAD4  58451   1       61608   61603   61611   61616   
CQUAD4  58452   1       61604   61605   61620   61618   
CQUAD4  58453   1       49751   49752   61621   61617   
CQUAD4  58454   1       49754   16582   16877   61613   
CQUAD4  58455   1       16778   16728   61615   61618   
CQUAD4  58456   1       16877   16827   61612   61613   
CQUAD4  100045  1       16640   16641   16646   16645   
CQUAD4  100046  1       16641   16642   16572   16646   
CQUAD4  100047  1       16642   16643   16647   16572   
CQUAD4  100048  1       16643   16644   16648   16647   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
