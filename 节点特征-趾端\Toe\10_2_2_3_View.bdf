$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:05:51                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    21              -380.   -7.034-80.      
GRID    22              -390.   -7.219-80.      
GRID    23              -400.   -7.404-80.      
GRID    24              -410.   -7.589-80.      
GRID    25              -420.   -7.774-80.      
GRID    26              -380.   10.     0.      
GRID    27              -390.   10.     0.      
GRID    28              -400.   10.     0.      
GRID    29              -410.   10.     0.      
GRID    30              -420.   10.     0.      
GRID    103             -400.   80.     0.      
GRID    113             -400.   80.     90.     
GRID    133             -400.   20.     0.      
GRID    183             -400.   30.     0.      
GRID    232             -400.   40.     0.      
GRID    282             -400.   50.     0.      
GRID    331             -400.   60.     0.      
GRID    381             -400.   70.     0.      
GRID    51946           -400.   80.     80.     
GRID    51947           -400.   80.     70.     
GRID    51948           -400.   80.     60.     
GRID    51949           -400.   80.     50.     
GRID    51950           -400.   80.     40.     
GRID    51951           -400.   80.     30.     
GRID    51952           -400.   80.     20.     
GRID    51953           -400.   80.     10.     
GRID    60579           -400.   65.     90.     
GRID    60580           -400.   10.     15.     
GRID    60581           -400.   10.     7.5     
GRID    60582           -400.   72.5    90.     
GRID    60583           -400.   64.4329879.76173
GRID    60584           -400.   20.1673916.32685
GRID    60585           -400.   29.7244320.041  
GRID    60586           -400.   38.2648225.71497
GRID    60587           -400.   45.6098532.86935
GRID    60588           -400.   51.6847441.12972
GRID    60589           -400.   56.5375150.16246
GRID    60590           -400.   60.2737159.71137
GRID    60591           -400.   62.9014669.62282
GRID    60592           -400.   71.5941428.59981
GRID    60593           -400.   69.2386119.40459
GRID    60594           -400.   59.6303310.15585
GRID    60595           -400.   58.5513 21.0608 
GRID    60596           -400.   68.9191959.35699
GRID    60597           -400.   70.8856869.4375 
GRID    60598           -400.   32.6283511.95781
GRID    60599           -400.   21.152199.879757
GRID    60600           -400.   66.8973547.78049
GRID    60601           -400.   49.1008111.47578
GRID    60602           -400.   59.6270637.31957
GRID    60603           -400.   51.9828227.58093
GRID    60604           -400.   43.0975918.78671
GRID    60605           -400.   70.1918238.20431
GRID    60606           -400.   71.9397779.70841
GRID    60607           -400.   70.     10.     
GRID    60608           -400.   63.5034 28.81465
GRID    60609           -400.   40.561397.296929
$==============================================================================$
CTRIA3  57689   1       60602   60600   60605   
CTRIA3  57690   1       60595   60594   60601   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  57658   1       60593   60607   60594   60595   
CQUAD4  57659   1       60583   60579   60582   60606   
CQUAD4  57660   1       60601   60604   60603   60595   
CQUAD4  57661   1       60606   51946   51947   60597   
CQUAD4  57662   1       60586   60604   60598   60585   
CQUAD4  57663   1       60602   60605   60592   60608   
CQUAD4  57664   1       60587   60603   60604   60586   
CQUAD4  57665   1       51948   51949   60600   60596   
CQUAD4  57666   1       51950   60605   60600   51949   
CQUAD4  57667   1       60600   60602   60588   60589   
CQUAD4  57668   1       381     60607   51953   103     
CQUAD4  57669   1       60598   183     133     60599   
CQUAD4  57670   1       232     183     60598   60609   
CQUAD4  57671   1       60599   133     28      60581   
CQUAD4  57672   1       60582   113     51946   60606   
CQUAD4  57673   1       60592   60593   60595   60608   
CQUAD4  57674   1       60589   60590   60596   60600   
CQUAD4  57675   1       60597   60596   60590   60591   
CQUAD4  57676   1       60601   282     232     60609   
CQUAD4  57677   1       51947   51948   60596   60597   
CQUAD4  57678   1       51950   51951   60592   60605   
CQUAD4  57679   1       60588   60602   60603   60587   
CQUAD4  57680   1       60609   60598   60604   60601   
CQUAD4  57681   1       60603   60602   60608   60595   
CQUAD4  57682   1       60585   60598   60599   60584   
CQUAD4  57683   1       51952   60593   60592   51951   
CQUAD4  57684   1       381     331     60594   60607   
CQUAD4  57685   1       282     60601   60594   331     
CQUAD4  57686   1       51953   60607   60593   51952   
CQUAD4  57687   1       60584   60599   60581   60580   
CQUAD4  57688   1       60591   60583   60606   60597   
CQUAD4  100009  1       21      22      27      26      
CQUAD4  100010  1       22      23      28      27      
CQUAD4  100011  1       23      24      29      28      
CQUAD4  100012  1       24      25      30      29      
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
