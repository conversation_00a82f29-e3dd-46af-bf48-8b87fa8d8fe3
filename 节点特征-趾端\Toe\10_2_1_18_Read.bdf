$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:04:49                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16578           2010.   1400.   0.      
GRID    16694           2000.   1380.   0.      
GRID    16695           2000.   1390.   0.      
GRID    16696           2000.   1400.   0.      
GRID    16697           2000.   1410.   0.      
GRID    16698           2000.   1420.   0.      
GRID    16699           2010.   1380.   0.      
GRID    16700           2010.   1390.   0.      
GRID    16701           2010.   1410.   0.      
GRID    16702           2010.   1420.   0.      
$==============================================================================$
CQUAD4  100069  1       16694   16695   16700   16699   
CQUAD4  100070  1       16695   16696   16578   16700   
CQUAD4  100071  1       16696   16697   16701   16578   
CQUAD4  100072  1       16697   16698   16702   16701   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
