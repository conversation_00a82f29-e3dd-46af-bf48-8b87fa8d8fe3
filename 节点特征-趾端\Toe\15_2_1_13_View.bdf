$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:39:04                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16573           3015.   600.    0.      
GRID    16583           3120.   600.    0.      
GRID    16593           3120.   600.    135.    
GRID    16649           3000.   570.    0.      
GRID    16650           3000.   585.    0.      
GRID    16651           3000.   600.    0.      
GRID    16652           3000.   615.    0.      
GRID    16653           3000.   630.    0.      
GRID    16654           3015.   570.    0.      
GRID    16655           3015.   585.    0.      
GRID    16656           3015.   615.    0.      
GRID    16657           3015.   630.    0.      
GRID    16733           3030.   600.    0.      
GRID    16783           3045.   600.    0.      
GRID    16832           3060.   600.    0.      
GRID    16882           3075.   600.    0.      
GRID    16931           3090.   600.    0.      
GRID    16981           3105.   600.    0.      
GRID    49775           3120.   600.    120.    
GRID    49776           3120.   600.    105.    
GRID    49777           3120.   600.    90.     
GRID    49778           3120.   600.    75.     
GRID    49779           3120.   600.    60.     
GRID    49780           3120.   600.    45.     
GRID    49781           3120.   600.    30.     
GRID    49782           3120.   600.    15.     
GRID    71341           3105.   600.    135.    
GRID    71342           3015.   600.    15.     
GRID    71343           3104.206600.    119.972 
GRID    71344           3029.952600.    16.70561
GRID    71345           3044.192600.    21.57155
GRID    71346           3057.2  600.    29.13774
GRID    71347           3068.705600.    38.83722
GRID    71348           3078.633600.    50.14682
GRID    71349           3086.823600.    62.77149
GRID    71350           3093.443600.    76.28589
GRID    71351           3098.486600.    90.46482
GRID    71352           3102.12 600.    105.0683
GRID    71353           3105.411600.    14.51462
GRID    71354           3090.842600.    14.11553
GRID    71355           3076.048600.    14.70329
GRID    71356           3091.691600.    28.27512
GRID    71357           3104.304600.    73.94908
GRID    71358           3063.268600.    18.15918
GRID    71359           3100.749600.    57.51653
GRID    71360           3046.233600.    13.26835
GRID    71361           3088.224600.    42.08906
GRID    71362           3080.055600.    26.08506
GRID    71363           3104.726600.    43.3857 
GRID    71364           3105.335600.    29.43759
GRID    71365           3074.232600.    32.55355
$==============================================================================$
CTRIA3  100860  1       71363   71361   71359   
CTRIA3  100861  1       71360   71344   71345   
CTRIA3  100862  1       71357   71350   71351   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100049  1       16649   16650   16655   16654   
CQUAD4  100050  1       16650   16651   16573   16655   
CQUAD4  100051  1       16651   16652   16656   16573   
CQUAD4  100052  1       16652   16653   16657   16656   
CQUAD4  100835  1       71353   71364   49781   49782   
CQUAD4  100836  1       16931   16882   71355   71354   
CQUAD4  100837  1       71357   71359   71349   71350   
CQUAD4  100838  1       49781   71364   71363   49780   
CQUAD4  100839  1       71344   16733   16573   71342   
CQUAD4  100840  1       16931   71354   71353   16981   
CQUAD4  100841  1       71343   71341   16593   49775   
CQUAD4  100842  1       71356   71364   71353   71354   
CQUAD4  100843  1       49776   49777   71351   71352   
CQUAD4  100844  1       71347   71348   71361   71365   
CQUAD4  100845  1       71356   71354   71355   71362   
CQUAD4  100846  1       16783   71360   71358   16832   
CQUAD4  100847  1       71362   71355   71358   71365   
CQUAD4  100848  1       71358   71346   71347   71365   
CQUAD4  100849  1       71348   71349   71359   71361   
CQUAD4  100850  1       16783   16733   71344   71360   
CQUAD4  100851  1       16981   71353   49782   16583   
CQUAD4  100852  1       49778   49779   71359   71357   
CQUAD4  100853  1       71363   71364   71356   71361   
CQUAD4  100854  1       49780   71363   71359   49779   
CQUAD4  100855  1       49775   49776   71352   71343   
CQUAD4  100856  1       49778   71357   71351   49777   
CQUAD4  100857  1       71361   71356   71362   71365   
CQUAD4  100858  1       71346   71358   71360   71345   
CQUAD4  100859  1       16832   71358   71355   16882   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
