$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:01:08                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    31              0.      580.    0.      
GRID    32              0.      590.    0.      
GRID    33              0.      600.    0.      
GRID    34              0.      610.    0.      
GRID    35              0.      620.    0.      
GRID    36              10.     580.    0.      
GRID    37              10.     590.    0.      
GRID    38              10.     600.    0.      
GRID    39              10.     610.    0.      
GRID    40              10.     620.    0.      
$==============================================================================$
CQUAD4  100013  1       31      32      37      36      
CQUAD4  100014  1       32      33      38      37      
CQUAD4  100015  1       33      34      39      38      
CQUAD4  100016  1       34      35      40      39      
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
