$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:37:53                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    41              0.      1170.   0.      
GRID    42              0.      1185.   0.      
GRID    43              0.      1200.   0.      
GRID    44              0.      1215.   0.      
GRID    45              0.      1230.   0.      
GRID    46              15.     1170.   0.      
GRID    47              15.     1185.   0.      
GRID    48              15.     1200.   0.      
GRID    49              15.     1215.   0.      
GRID    50              15.     1230.   0.      
GRID    105             180.    1200.   0.      
GRID    115             180.    1200.   180.    
GRID    143             30.     1200.   0.      
GRID    193             45.     1200.   0.      
GRID    242             60.     1200.   0.      
GRID    292             75.     1200.   0.      
GRID    341             90.     1200.   0.      
GRID    391             105.    1200.   0.      
GRID    440             120.    1200.   0.      
GRID    490             135.    1200.   0.      
GRID    539             150.    1200.   0.      
GRID    589             165.    1200.   0.      
GRID    51962           180.    1200.   165.    
GRID    51963           180.    1200.   150.    
GRID    51964           180.    1200.   135.    
GRID    51965           180.    1200.   120.    
GRID    51966           180.    1200.   105.    
GRID    51967           180.    1200.   90.     
GRID    51968           180.    1200.   75.     
GRID    51969           180.    1200.   60.     
GRID    51970           180.    1200.   45.     
GRID    51971           180.    1200.   30.     
GRID    51972           180.    1200.   15.     
GRID    70477           165.    1200.   180.    
GRID    70478           15.     1200.   15.     
GRID    70479           164.40961200.   165.4588
GRID    70480           29.530951200.   15.80358
GRID    70481           43.896841200.   18.1306 
GRID    70482           57.941591200.   21.94351
GRID    70483           71.518821200.   27.18296
GRID    70484           84.490281200.   33.781  
GRID    70485           96.720751200.   41.66823
GRID    70486           108.13111200.   50.70133
GRID    70487           118.62251200.   60.78716
GRID    70488           128.15441200.   71.78422
GRID    70489           136.67991200.   83.5786 
GRID    70490           144.09281200.   96.10227
GRID    70491           150.43881200.   109.1989
GRID    70492           155.65791200.   122.7839
GRID    70493           159.733 1200.   136.7548
GRID    70494           162.65271200.   151.0121
GRID    70495           153.56821200.   58.13178
GRID    70496           121.82241200.   27.37471
GRID    70497           148.71721200.   44.3185 
GRID    70498           133.87831200.   30.38507
GRID    70499           164.27271200.   104.5034
GRID    70500           75.490361200.   15.9285 
GRID    70501           165.85  1200.   120.4246
GRID    70502           93.986011200.   19.42911
GRID    70503           160.42771200.   88.98135
GRID    70504           147.89171200.   77.68674
GRID    70505           139.516 1200.   62.77402
GRID    70506           131.19821200.   47.1932 
GRID    70507           116.73431200.   40.18838
GRID    70508           103.79661200.   33.05079
GRID    70509           121.34581200.   13.62104
GRID    70510           107.615 1200.   14.09851
GRID    70511           165.    1200.   15.     
GRID    70512           166.298 1200.   74.65051
GRID    70513           166.71451200.   58.56164
GRID    70514           150.    1200.   15.     
GRID    70515           135.32681200.   14.6743 
GRID    70516           164.47781200.   43.60722
GRID    70517           165.    1200.   30.     
GRID    70518           149.35821200.   30.07149
GRID    70519           155.86331200.   70.90848
GRID    70520           111.09981200.   24.96382
$==============================================================================$
CTRIA3  100252  1       70500   70482   70483   
CTRIA3  100253  1       70501   70492   70493   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100017  1       41      42      47      46      
CQUAD4  100018  1       42      43      48      47      
CQUAD4  100019  1       43      44      49      48      
CQUAD4  100020  1       44      45      50      49      
CQUAD4  100207  1       70493   51964   51965   70501   
CQUAD4  100208  1       51969   51970   70516   70513   
CQUAD4  100209  1       70515   70514   539     490     
CQUAD4  100210  1       51972   70511   70517   51971   
CQUAD4  100211  1       70482   242     193     70481   
CQUAD4  100212  1       70483   70484   70502   70500   
CQUAD4  100213  1       391     341     70502   70510   
CQUAD4  100214  1       292     70500   70502   341     
CQUAD4  100215  1       440     391     70510   70509   
CQUAD4  100216  1       70517   70518   70497   70516   
CQUAD4  100217  1       70479   70477   115     51962   
CQUAD4  100218  1       70503   70504   70489   70490   
CQUAD4  100219  1       51968   70512   70503   51967   
CQUAD4  100220  1       70489   70504   70505   70488   
CQUAD4  100221  1       70513   70516   70497   70495   
CQUAD4  100222  1       490     440     70509   70515   
CQUAD4  100223  1       51965   51966   70499   70501   
CQUAD4  100224  1       70506   70497   70518   70498   
CQUAD4  100225  1       70508   70485   70486   70507   
CQUAD4  100226  1       70480   143     48      70478   
CQUAD4  100227  1       70510   70520   70496   70509   
CQUAD4  100228  1       589     539     70514   70511   
CQUAD4  100229  1       51970   51971   70517   70516   
CQUAD4  100230  1       70492   70501   70499   70491   
CQUAD4  100231  1       70482   70500   292     242     
CQUAD4  100232  1       70495   70505   70504   70519   
CQUAD4  100233  1       143     70480   70481   193     
CQUAD4  100234  1       70506   70487   70488   70505   
CQUAD4  100235  1       70487   70506   70507   70486   
CQUAD4  100236  1       70506   70498   70496   70507   
CQUAD4  100237  1       70504   70503   70512   70519   
CQUAD4  100238  1       70509   70496   70498   70515   
CQUAD4  100239  1       589     70511   51972   105     
CQUAD4  100240  1       70493   70494   51963   51964   
CQUAD4  100241  1       70491   70499   70503   70490   
CQUAD4  100242  1       70485   70508   70502   70484   
CQUAD4  100243  1       70510   70502   70508   70520   
CQUAD4  100244  1       51962   51963   70494   70479   
CQUAD4  100245  1       51968   51969   70513   70512   
CQUAD4  100246  1       51966   51967   70503   70499   
CQUAD4  100247  1       70495   70519   70512   70513   
CQUAD4  100248  1       70517   70511   70514   70518   
CQUAD4  100249  1       70508   70507   70496   70520   
CQUAD4  100250  1       70506   70505   70495   70497   
CQUAD4  100251  1       70515   70498   70518   70514   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
