$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:01:09                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    61              0.      1180.   0.      
GRID    62              0.      1190.   0.      
GRID    63              0.      1200.   0.      
GRID    64              0.      1210.   0.      
GRID    65              0.      1220.   0.      
GRID    66              10.     1180.   0.      
GRID    67              10.     1190.   0.      
GRID    68              10.     1200.   0.      
GRID    69              10.     1210.   0.      
GRID    70              10.     1220.   0.      
$==============================================================================$
CQUAD4  100025  1       61      62      67      66      
CQUAD4  100026  1       62      63      68      67      
CQUAD4  100027  1       63      64      69      68      
CQUAD4  100028  1       64      65      70      69      
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
