$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:01:08                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    11              0.      180.    0.      
GRID    12              0.      190.    0.      
GRID    13              0.      200.    0.      
GRID    14              0.      210.    0.      
GRID    15              0.      220.    0.      
GRID    16              10.     180.    0.      
GRID    17              10.     190.    0.      
GRID    18              10.     200.    0.      
GRID    19              10.     210.    0.      
GRID    20              10.     220.    0.      
GRID    102             60.     200.    0.      
GRID    112             60.     200.    70.     
GRID    128             20.     200.    0.      
GRID    178             30.     200.    0.      
GRID    227             40.     200.    0.      
GRID    277             50.     200.    0.      
GRID    51940           60.     200.    60.     
GRID    51941           60.     200.    50.     
GRID    51942           60.     200.    40.     
GRID    51943           60.     200.    30.     
GRID    51944           60.     200.    20.     
GRID    51945           60.     200.    10.     
GRID    60557           45.     200.    70.     
GRID    60558           10.     200.    15.     
GRID    60559           44.37878200.    59.82797
GRID    60560           19.92168200.    17.3105 
GRID    60561           28.26979200.    23.15039
GRID    60562           34.69925200.    31.05514
GRID    60563           39.3438 200.    40.12539
GRID    60564           42.52186200.    49.80776
GRID    60565           10.     200.    7.5     
GRID    60566           52.5    200.    70.     
GRID    60567           51.23794200.    59.71303
GRID    60568           40.41015200.    10.62908
GRID    60569           50.50043200.    9.399915
GRID    60570           51.87921200.    18.74641
GRID    60571           19.00226200.    8.534843
GRID    60572           49.77278200.    49.28801
GRID    60573           48.01475200.    37.44856
GRID    60574           28.20004200.    9.721301
GRID    60575           40.31733200.    26.4227 
GRID    60576           34.37307200.    17.28417
GRID    60577           50.70051200.    28.17791
GRID    60578           44.80686200.    18.48744
$==============================================================================$
CTRIA3  57655   1       60574   60571   60560   
CTRIA3  57656   1       60577   60575   60573   
CTRIA3  57657   1       60574   60576   60568   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  57634   1       60573   60572   51941   51942   
CQUAD4  57635   1       51940   60567   60566   112     
CQUAD4  57636   1       60575   60577   60570   60578   
CQUAD4  57637   1       60563   60564   60572   60573   
CQUAD4  57638   1       60571   128     18      60565   
CQUAD4  57639   1       60559   60557   60566   60567   
CQUAD4  57640   1       60575   60578   60568   60576   
CQUAD4  57641   1       60570   51944   51945   60569   
CQUAD4  57642   1       60575   60562   60563   60573   
CQUAD4  57643   1       51944   60570   60577   51943   
CQUAD4  57644   1       60562   60575   60576   60561   
CQUAD4  57645   1       60570   60569   60568   60578   
CQUAD4  57646   1       60565   60558   60560   60571   
CQUAD4  57647   1       60574   60568   227     178     
CQUAD4  57648   1       60567   51940   51941   60572   
CQUAD4  57649   1       60564   60559   60567   60572   
CQUAD4  57650   1       60560   60561   60576   60574   
CQUAD4  57651   1       51942   51943   60577   60573   
CQUAD4  57652   1       51945   102     277     60569   
CQUAD4  57653   1       178     128     60571   60574   
CQUAD4  57654   1       277     227     60568   60569   
CQUAD4  100005  1       11      12      17      16      
CQUAD4  100006  1       12      13      18      17      
CQUAD4  100007  1       13      14      19      18      
CQUAD4  100008  1       14      15      20      19      
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
