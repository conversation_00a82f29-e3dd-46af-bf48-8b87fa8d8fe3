$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:37:59                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16603           6015.   600.    0.      
GRID    16613           6120.   600.    0.      
GRID    16623           6120.   600.    135.    
GRID    33189           6000.   570.    0.      
GRID    33190           6000.   585.    0.      
GRID    33191           6000.   600.    0.      
GRID    33192           6000.   615.    0.      
GRID    33193           6000.   630.    0.      
GRID    33194           6015.   570.    0.      
GRID    33195           6015.   585.    0.      
GRID    33196           6015.   615.    0.      
GRID    33197           6015.   630.    0.      
GRID    33273           6030.   600.    0.      
GRID    33323           6045.   600.    0.      
GRID    33372           6060.   600.    0.      
GRID    33422           6075.   600.    0.      
GRID    33471           6090.   600.    0.      
GRID    33521           6105.   600.    0.      
GRID    50427           6120.   600.    120.    
GRID    50428           6120.   600.    105.    
GRID    50429           6120.   600.    90.     
GRID    50430           6120.   600.    75.     
GRID    50431           6120.   600.    60.     
GRID    50432           6120.   600.    45.     
GRID    50433           6120.   600.    30.     
GRID    50434           6120.   600.    15.     
GRID    72259           6105.   600.    135.    
GRID    72260           6015.   600.    15.     
GRID    72261           6104.206600.    119.972 
GRID    72262           6029.952600.    16.70561
GRID    72263           6044.192600.    21.57155
GRID    72264           6057.2  600.    29.13774
GRID    72265           6068.705600.    38.83722
GRID    72266           6078.633600.    50.14682
GRID    72267           6086.823600.    62.77149
GRID    72268           6093.443600.    76.28589
GRID    72269           6098.486600.    90.46482
GRID    72270           6102.12 600.    105.0683
GRID    72271           6105.411600.    14.51462
GRID    72272           6090.842600.    14.11553
GRID    72273           6076.048600.    14.70329
GRID    72274           6091.691600.    28.27512
GRID    72275           6104.304600.    73.94908
GRID    72276           6063.268600.    18.15918
GRID    72277           6100.749600.    57.51653
GRID    72278           6046.233600.    13.26835
GRID    72279           6088.224600.    42.08906
GRID    72280           6080.055600.    26.08506
GRID    72281           6104.726600.    43.3857 
GRID    72282           6105.335600.    29.43759
GRID    72283           6074.232600.    32.55355
$==============================================================================$
CTRIA3  101548  1       72281   72279   72277   
CTRIA3  101549  1       72278   72262   72263   
CTRIA3  101550  1       72275   72268   72269   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100089  1       33189   33190   33195   33194   
CQUAD4  100090  1       33190   33191   16603   33195   
CQUAD4  100091  1       33191   33192   33196   16603   
CQUAD4  100092  1       33192   33193   33197   33196   
CQUAD4  101523  1       72271   72282   50433   50434   
CQUAD4  101524  1       33471   33422   72273   72272   
CQUAD4  101525  1       72275   72277   72267   72268   
CQUAD4  101526  1       50433   72282   72281   50432   
CQUAD4  101527  1       72262   33273   16603   72260   
CQUAD4  101528  1       33471   72272   72271   33521   
CQUAD4  101529  1       72261   72259   16623   50427   
CQUAD4  101530  1       72274   72282   72271   72272   
CQUAD4  101531  1       50428   50429   72269   72270   
CQUAD4  101532  1       72265   72266   72279   72283   
CQUAD4  101533  1       72274   72272   72273   72280   
CQUAD4  101534  1       33323   72278   72276   33372   
CQUAD4  101535  1       72280   72273   72276   72283   
CQUAD4  101536  1       72276   72264   72265   72283   
CQUAD4  101537  1       72266   72267   72277   72279   
CQUAD4  101538  1       33323   33273   72262   72278   
CQUAD4  101539  1       33521   72271   50434   16613   
CQUAD4  101540  1       50430   50431   72277   72275   
CQUAD4  101541  1       72281   72282   72274   72279   
CQUAD4  101542  1       50432   72281   72277   50431   
CQUAD4  101543  1       50427   50428   72270   72261   
CQUAD4  101544  1       50430   72275   72269   50429   
CQUAD4  101545  1       72279   72274   72280   72283   
CQUAD4  101546  1       72264   72276   72278   72263   
CQUAD4  101547  1       33372   72276   72273   33422   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
