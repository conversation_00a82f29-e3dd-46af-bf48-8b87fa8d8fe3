$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:01:09                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    41              0.      780.    0.      
GRID    42              0.      790.    0.      
GRID    43              0.      800.    0.      
GRID    44              0.      810.    0.      
GRID    45              0.      820.    0.      
GRID    46              10.     780.    0.      
GRID    47              10.     790.    0.      
GRID    48              10.     800.    0.      
GRID    49              10.     810.    0.      
GRID    50              10.     820.    0.      
$==============================================================================$
CQUAD4  100017  1       41      42      47      46      
CQUAD4  100018  1       42      43      48      47      
CQUAD4  100019  1       43      44      49      48      
CQUAD4  100020  1       44      45      50      49      
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
