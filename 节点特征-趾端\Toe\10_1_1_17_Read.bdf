$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:01:12                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16577           2010.   1200.   0.      
GRID    16685           2000.   1180.   0.      
GRID    16686           2000.   1190.   0.      
GRID    16687           2000.   1200.   0.      
GRID    16688           2000.   1210.   0.      
GRID    16689           2000.   1220.   0.      
GRID    16690           2010.   1180.   0.      
GRID    16691           2010.   1190.   0.      
GRID    16692           2010.   1210.   0.      
GRID    16693           2010.   1220.   0.      
$==============================================================================$
CQUAD4  100065  1       16685   16686   16691   16690   
CQUAD4  100066  1       16686   16687   16577   16691   
CQUAD4  100067  1       16687   16688   16692   16577   
CQUAD4  100068  1       16688   16689   16693   16692   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
