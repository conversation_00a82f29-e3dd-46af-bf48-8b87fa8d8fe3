$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:03:45                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16603           -400.   4010.   0.      
GRID    16613           -400.   4080.   0.      
GRID    16623           -400.   4080.   90.     
GRID    33189           -380.   4000.   0.      
GRID    33190           -390.   4000.   0.      
GRID    33191           -400.   4000.   0.      
GRID    33192           -410.   4000.   0.      
GRID    33193           -420.   4000.   0.      
GRID    33194           -380.   4010.   0.      
GRID    33195           -390.   4010.   0.      
GRID    33196           -410.   4010.   0.      
GRID    33197           -420.   4010.   0.      
GRID    33273           -400.   4020.   0.      
GRID    33323           -400.   4030.   0.      
GRID    33372           -400.   4040.   0.      
GRID    33422           -400.   4050.   0.      
GRID    33471           -400.   4060.   0.      
GRID    33521           -400.   4070.   0.      
GRID    50427           -400.   4080.   80.     
GRID    50428           -400.   4080.   70.     
GRID    50429           -400.   4080.   60.     
GRID    50430           -400.   4080.   50.     
GRID    50431           -400.   4080.   40.     
GRID    50432           -400.   4080.   30.     
GRID    50433           -400.   4080.   20.     
GRID    50434           -400.   4080.   10.     
GRID    62667           -400.   4065.   90.     
GRID    62668           -400.   4010.   15.     
GRID    62669           -400.   4010.   7.5     
GRID    62670           -400.   4072.5  90.     
GRID    62671           -400.   4064.43379.76173
GRID    62672           -400.   4020.16716.32685
GRID    62673           -400.   4029.72420.041  
GRID    62674           -400.   4038.26525.71497
GRID    62675           -400.   4045.61 32.86935
GRID    62676           -400.   4051.68541.12972
GRID    62677           -400.   4056.53850.16246
GRID    62678           -400.   4060.27459.71137
GRID    62679           -400.   4062.90169.62282
GRID    62680           -400.   4071.59428.59981
GRID    62681           -400.   4069.23919.40459
GRID    62682           -400.   4059.63 10.15585
GRID    62683           -400.   4058.55121.0608 
GRID    62684           -400.   4068.91959.35699
GRID    62685           -400.   4070.88669.4375 
GRID    62686           -400.   4032.62811.95781
GRID    62687           -400.   4021.1529.879757
GRID    62688           -400.   4066.89747.78049
GRID    62689           -400.   4049.10111.47578
GRID    62690           -400.   4059.62737.31957
GRID    62691           -400.   4051.98327.58093
GRID    62692           -400.   4043.09818.78671
GRID    62693           -400.   4070.19238.20431
GRID    62694           -400.   4071.94 79.70841
GRID    62695           -400.   4070.   10.     
GRID    62696           -400.   4063.50328.81465
GRID    62697           -400.   4040.5617.296929
$==============================================================================$
CTRIA3  59293   1       62690   62688   62693   
CTRIA3  59294   1       62683   62682   62689   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  59262   1       62681   62695   62682   62683   
CQUAD4  59263   1       62671   62667   62670   62694   
CQUAD4  59264   1       62689   62692   62691   62683   
CQUAD4  59265   1       62694   50427   50428   62685   
CQUAD4  59266   1       62674   62692   62686   62673   
CQUAD4  59267   1       62690   62693   62680   62696   
CQUAD4  59268   1       62675   62691   62692   62674   
CQUAD4  59269   1       50429   50430   62688   62684   
CQUAD4  59270   1       50431   62693   62688   50430   
CQUAD4  59271   1       62688   62690   62676   62677   
CQUAD4  59272   1       33521   62695   50434   16613   
CQUAD4  59273   1       62686   33323   33273   62687   
CQUAD4  59274   1       33372   33323   62686   62697   
CQUAD4  59275   1       62687   33273   16603   62669   
CQUAD4  59276   1       62670   16623   50427   62694   
CQUAD4  59277   1       62680   62681   62683   62696   
CQUAD4  59278   1       62677   62678   62684   62688   
CQUAD4  59279   1       62685   62684   62678   62679   
CQUAD4  59280   1       62689   33422   33372   62697   
CQUAD4  59281   1       50428   50429   62684   62685   
CQUAD4  59282   1       50431   50432   62680   62693   
CQUAD4  59283   1       62676   62690   62691   62675   
CQUAD4  59284   1       62697   62686   62692   62689   
CQUAD4  59285   1       62691   62690   62696   62683   
CQUAD4  59286   1       62673   62686   62687   62672   
CQUAD4  59287   1       50433   62681   62680   50432   
CQUAD4  59288   1       33521   33471   62682   62695   
CQUAD4  59289   1       33422   62689   62682   33471   
CQUAD4  59290   1       50434   62695   62681   50433   
CQUAD4  59291   1       62672   62687   62669   62668   
CQUAD4  59292   1       62679   62671   62694   62685   
CQUAD4  100089  1       33189   33190   33195   33194   
CQUAD4  100090  1       33190   33191   16603   33195   
CQUAD4  100091  1       33191   33192   33196   16603   
CQUAD4  100092  1       33192   33193   33197   33196   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
