$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:39:34                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16602           -300.   6015.   0.      
GRID    16612           -300.   6090.   0.      
GRID    16622           -300.   6090.   105.    
GRID    33180           -270.   6000.   0.      
GRID    33181           -285.   6000.   0.      
GRID    33182           -300.   6000.   0.      
GRID    33183           -315.   6000.   0.      
GRID    33184           -330.   6000.   0.      
GRID    33185           -270.   6015.   0.      
GRID    33186           -285.   6015.   0.      
GRID    33187           -315.   6015.   0.      
GRID    33188           -330.   6015.   0.      
GRID    33268           -300.   6030.   0.      
GRID    33318           -300.   6045.   0.      
GRID    33367           -300.   6060.   0.      
GRID    33417           -300.   6075.   0.      
GRID    50352           -300.   6090.   90.     
GRID    50353           -300.   6090.   75.     
GRID    50354           -300.   6090.   60.     
GRID    50355           -300.   6090.   45.     
GRID    50356           -300.   6090.   30.     
GRID    50357           -300.   6090.   15.     
GRID    72244           -300.   6075.   105.    
GRID    72245           -300.   6015.   15.     
GRID    72246           -300.   6074.15990.18579
GRID    72247           -300.   6029.57717.75945
GRID    72248           -300.   6042.47125.09506
GRID    72249           -300.   6053.05935.48817
GRID    72250           -300.   6061.30347.82362
GRID    72251           -300.   6067.42461.34004
GRID    72252           -300.   6071.66275.55959
GRID    72253           -300.   6077.36659.02478
GRID    72254           -300.   6072.48432.50837
GRID    72255           -300.   6058.43418.00521
GRID    72256           -300.   6073.17416.89507
GRID    72257           -300.   6075.20245.69008
GRID    72258           -300.   6045.69114.46414
$==============================================================================$
CTRIA3  101521  1       72253   72251   72252   
CTRIA3  101522  1       72258   72247   72248   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100085  1       33180   33181   33186   33185   
CQUAD4  100086  1       33181   33182   16602   33186   
CQUAD4  100087  1       33182   33183   33187   16602   
CQUAD4  100088  1       33183   33184   33188   33187   
CQUAD4  101506  1       50354   72253   72252   50353   
CQUAD4  101507  1       50355   50356   72254   72257   
CQUAD4  101508  1       72258   72248   72249   72255   
CQUAD4  101509  1       72249   72250   72257   72254   
CQUAD4  101510  1       33318   33268   72247   72258   
CQUAD4  101511  1       50352   50353   72252   72246   
CQUAD4  101512  1       72251   72253   72257   72250   
CQUAD4  101513  1       72255   72256   33417   33367   
CQUAD4  101514  1       50354   50355   72257   72253   
CQUAD4  101515  1       72255   33367   33318   72258   
CQUAD4  101516  1       72246   72244   16622   50352   
CQUAD4  101517  1       72256   72255   72249   72254   
CQUAD4  101518  1       50357   16612   33417   72256   
CQUAD4  101519  1       72254   50356   50357   72256   
CQUAD4  101520  1       72247   33268   16602   72245   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
