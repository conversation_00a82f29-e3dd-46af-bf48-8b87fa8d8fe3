$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:39:04                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16572           3015.   300.    0.      
GRID    16582           3090.   300.    0.      
GRID    16592           3090.   300.    105.    
GRID    16640           3000.   270.    0.      
GRID    16641           3000.   285.    0.      
GRID    16642           3000.   300.    0.      
GRID    16643           3000.   315.    0.      
GRID    16644           3000.   330.    0.      
GRID    16645           3015.   270.    0.      
GRID    16646           3015.   285.    0.      
GRID    16647           3015.   315.    0.      
GRID    16648           3015.   330.    0.      
GRID    16728           3030.   300.    0.      
GRID    16778           3045.   300.    0.      
GRID    16827           3060.   300.    0.      
GRID    16877           3075.   300.    0.      
GRID    49749           3090.   300.    90.     
GRID    49750           3090.   300.    75.     
GRID    49751           3090.   300.    60.     
GRID    49752           3090.   300.    45.     
GRID    49753           3090.   300.    30.     
GRID    49754           3090.   300.    15.     
GRID    71326           3075.   300.    105.    
GRID    71327           3015.   300.    15.     
GRID    71328           3074.159300.    90.18579
GRID    71329           3029.577300.    17.75945
GRID    71330           3042.471300.    25.09506
GRID    71331           3053.059300.    35.48817
GRID    71332           3061.303300.    47.82362
GRID    71333           3067.424300.    61.34004
GRID    71334           3071.662300.    75.55959
GRID    71335           3077.366300.    59.02478
GRID    71336           3072.484300.    32.50837
GRID    71337           3058.434300.    18.00521
GRID    71338           3073.174300.    16.89507
GRID    71339           3075.202300.    45.69008
GRID    71340           3045.691300.    14.46414
$==============================================================================$
CTRIA3  100833  1       71335   71333   71334   
CTRIA3  100834  1       71340   71329   71330   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100045  1       16640   16641   16646   16645   
CQUAD4  100046  1       16641   16642   16572   16646   
CQUAD4  100047  1       16642   16643   16647   16572   
CQUAD4  100048  1       16643   16644   16648   16647   
CQUAD4  100818  1       49751   71335   71334   49750   
CQUAD4  100819  1       49752   49753   71336   71339   
CQUAD4  100820  1       71340   71330   71331   71337   
CQUAD4  100821  1       71331   71332   71339   71336   
CQUAD4  100822  1       16778   16728   71329   71340   
CQUAD4  100823  1       49749   49750   71334   71328   
CQUAD4  100824  1       71333   71335   71339   71332   
CQUAD4  100825  1       71337   71338   16877   16827   
CQUAD4  100826  1       49751   49752   71339   71335   
CQUAD4  100827  1       71337   16827   16778   71340   
CQUAD4  100828  1       71328   71326   16592   49749   
CQUAD4  100829  1       71338   71337   71331   71336   
CQUAD4  100830  1       49754   16582   16877   71338   
CQUAD4  100831  1       71336   49753   49754   71338   
CQUAD4  100832  1       71329   16728   16572   71327   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
