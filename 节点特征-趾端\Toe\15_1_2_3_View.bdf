$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:38:34                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    21              -570.   -1.055-70.      
GRID    22              -585.   -1.083-70.      
GRID    23              -600.   -1.111-70.      
GRID    24              -615.   -1.138-70.      
GRID    25              -630.   -1.166-70.      
GRID    26              -570.   15.     0.      
GRID    27              -585.   15.     0.      
GRID    28              -600.   15.     0.      
GRID    29              -615.   15.     0.      
GRID    30              -630.   15.     0.      
GRID    103             -600.   120.    0.      
GRID    113             -600.   120.    135.    
GRID    133             -600.   30.     0.      
GRID    183             -600.   45.     0.      
GRID    232             -600.   60.     0.      
GRID    282             -600.   75.     0.      
GRID    331             -600.   90.     0.      
GRID    381             -600.   105.    0.      
GRID    51946           -600.   120.    120.    
GRID    51947           -600.   120.    105.    
GRID    51948           -600.   120.    90.     
GRID    51949           -600.   120.    75.     
GRID    51950           -600.   120.    60.     
GRID    51951           -600.   120.    45.     
GRID    51952           -600.   120.    30.     
GRID    51953           -600.   120.    15.     
GRID    70423           -600.   105.    135.    
GRID    70424           -600.   15.     15.     
GRID    70425           -600.   104.2059119.972 
GRID    70426           -600.   29.9518416.70561
GRID    70427           -600.   44.192  21.57155
GRID    70428           -600.   57.2000129.13774
GRID    70429           -600.   68.7054738.83722
GRID    70430           -600.   78.6327450.14682
GRID    70431           -600.   86.8230862.77149
GRID    70432           -600.   93.4432476.28589
GRID    70433           -600.   98.4857390.46482
GRID    70434           -600.   102.1201105.0683
GRID    70435           -600.   105.411114.51462
GRID    70436           -600.   90.8416514.11553
GRID    70437           -600.   76.0481714.70329
GRID    70438           -600.   91.6908528.27512
GRID    70439           -600.   104.303673.94908
GRID    70440           -600.   63.2676918.15918
GRID    70441           -600.   100.748757.51653
GRID    70442           -600.   46.2325513.26835
GRID    70443           -600.   88.2237142.08906
GRID    70444           -600.   80.0554226.08506
GRID    70445           -600.   104.725643.3857 
GRID    70446           -600.   105.334729.43759
GRID    70447           -600.   74.2317 32.55355
$==============================================================================$
CTRIA3  100172  1       70445   70443   70441   
CTRIA3  100173  1       70442   70426   70427   
CTRIA3  100174  1       70439   70432   70433   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100009  1       21      22      27      26      
CQUAD4  100010  1       22      23      28      27      
CQUAD4  100011  1       23      24      29      28      
CQUAD4  100012  1       24      25      30      29      
CQUAD4  100147  1       70435   70446   51952   51953   
CQUAD4  100148  1       331     282     70437   70436   
CQUAD4  100149  1       70439   70441   70431   70432   
CQUAD4  100150  1       51952   70446   70445   51951   
CQUAD4  100151  1       70426   133     28      70424   
CQUAD4  100152  1       331     70436   70435   381     
CQUAD4  100153  1       70425   70423   113     51946   
CQUAD4  100154  1       70438   70446   70435   70436   
CQUAD4  100155  1       51947   51948   70433   70434   
CQUAD4  100156  1       70429   70430   70443   70447   
CQUAD4  100157  1       70438   70436   70437   70444   
CQUAD4  100158  1       183     70442   70440   232     
CQUAD4  100159  1       70444   70437   70440   70447   
CQUAD4  100160  1       70440   70428   70429   70447   
CQUAD4  100161  1       70430   70431   70441   70443   
CQUAD4  100162  1       183     133     70426   70442   
CQUAD4  100163  1       381     70435   51953   103     
CQUAD4  100164  1       51949   51950   70441   70439   
CQUAD4  100165  1       70445   70446   70438   70443   
CQUAD4  100166  1       51951   70445   70441   51950   
CQUAD4  100167  1       51946   51947   70434   70425   
CQUAD4  100168  1       51949   70439   70433   51948   
CQUAD4  100169  1       70443   70438   70444   70447   
CQUAD4  100170  1       70428   70440   70442   70427   
CQUAD4  100171  1       232     70440   70437   282     
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
