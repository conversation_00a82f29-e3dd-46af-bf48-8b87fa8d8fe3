$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:01:14                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16602           4010.   200.    0.      
GRID    16612           4060.   200.    0.      
GRID    16622           4060.   200.    70.     
GRID    33180           4000.   180.    0.      
GRID    33181           4000.   190.    0.      
GRID    33182           4000.   200.    0.      
GRID    33183           4000.   210.    0.      
GRID    33184           4000.   220.    0.      
GRID    33185           4010.   180.    0.      
GRID    33186           4010.   190.    0.      
GRID    33187           4010.   210.    0.      
GRID    33188           4010.   220.    0.      
GRID    33268           4020.   200.    0.      
GRID    33318           4030.   200.    0.      
GRID    33367           4040.   200.    0.      
GRID    33417           4050.   200.    0.      
GRID    50352           4060.   200.    60.     
GRID    50353           4060.   200.    50.     
GRID    50354           4060.   200.    40.     
GRID    50355           4060.   200.    30.     
GRID    50356           4060.   200.    20.     
GRID    50357           4060.   200.    10.     
GRID    62645           4045.   200.    70.     
GRID    62646           4010.   200.    15.     
GRID    62647           4044.379200.    59.82797
GRID    62648           4019.922200.    17.3105 
GRID    62649           4028.27 200.    23.15039
GRID    62650           4034.699200.    31.05514
GRID    62651           4039.344200.    40.12539
GRID    62652           4042.522200.    49.80776
GRID    62653           4010.   200.    7.5     
GRID    62654           4052.5  200.    70.     
GRID    62655           4051.238200.    59.71303
GRID    62656           4040.41 200.    10.62908
GRID    62657           4050.5  200.    9.399915
GRID    62658           4051.879200.    18.74641
GRID    62659           4019.002200.    8.534843
GRID    62660           4049.773200.    49.28801
GRID    62661           4048.015200.    37.44856
GRID    62662           4028.2  200.    9.721301
GRID    62663           4040.317200.    26.4227 
GRID    62664           4034.373200.    17.28417
GRID    62665           4050.701200.    28.17791
GRID    62666           4044.807200.    18.48744
$==============================================================================$
CTRIA3  59259   1       62662   62659   62648   
CTRIA3  59260   1       62665   62663   62661   
CTRIA3  59261   1       62662   62664   62656   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  59238   1       62661   62660   50353   50354   
CQUAD4  59239   1       50352   62655   62654   16622   
CQUAD4  59240   1       62663   62665   62658   62666   
CQUAD4  59241   1       62651   62652   62660   62661   
CQUAD4  59242   1       62659   33268   16602   62653   
CQUAD4  59243   1       62647   62645   62654   62655   
CQUAD4  59244   1       62663   62666   62656   62664   
CQUAD4  59245   1       62658   50356   50357   62657   
CQUAD4  59246   1       62663   62650   62651   62661   
CQUAD4  59247   1       50356   62658   62665   50355   
CQUAD4  59248   1       62650   62663   62664   62649   
CQUAD4  59249   1       62658   62657   62656   62666   
CQUAD4  59250   1       62653   62646   62648   62659   
CQUAD4  59251   1       62662   62656   33367   33318   
CQUAD4  59252   1       62655   50352   50353   62660   
CQUAD4  59253   1       62652   62647   62655   62660   
CQUAD4  59254   1       62648   62649   62664   62662   
CQUAD4  59255   1       50354   50355   62665   62661   
CQUAD4  59256   1       50357   16612   33417   62657   
CQUAD4  59257   1       33318   33268   62659   62662   
CQUAD4  59258   1       33417   33367   62656   62657   
CQUAD4  100085  1       33180   33181   33186   33185   
CQUAD4  100086  1       33181   33182   16602   33186   
CQUAD4  100087  1       33182   33183   33187   16602   
CQUAD4  100088  1       33183   33184   33188   33187   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
