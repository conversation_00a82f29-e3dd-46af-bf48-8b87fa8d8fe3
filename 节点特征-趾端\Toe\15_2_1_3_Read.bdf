$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:39:01                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    21              0.      570.    0.      
GRID    22              0.      585.    0.      
GRID    23              0.      600.    0.      
GRID    24              0.      615.    0.      
GRID    25              0.      630.    0.      
GRID    26              15.     570.    0.      
GRID    27              15.     585.    0.      
GRID    28              15.     600.    0.      
GRID    29              15.     615.    0.      
GRID    30              15.     630.    0.      
$==============================================================================$
CQUAD4  100009  1       21      22      27      26      
CQUAD4  100010  1       22      23      28      27      
CQUAD4  100011  1       23      24      29      28      
CQUAD4  100012  1       24      25      30      29      
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
