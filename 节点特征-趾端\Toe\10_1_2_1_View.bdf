$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:03:38                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    1               20.     3.7021-90.      
GRID    2               10.     1.851-9 0.      
GRID    3               0.      0.      0.      
GRID    4               -10.    -1.851-90.      
GRID    5               -20.    -3.702-90.      
GRID    6               20.     10.     0.      
GRID    7               10.     10.     0.      
GRID    8               -1.851-910.     0.      
GRID    9               -10.    10.     0.      
GRID    10              -20.    10.     0.      
GRID    101             -7.404-940.     0.      
GRID    111             -7.404-940.     50.     
GRID    123             -3.702-920.     0.      
GRID    173             -5.553-930.     0.      
GRID    51936           -7.404-940.     40.     
GRID    51937           -7.404-940.     30.     
GRID    51938           -7.404-940.     20.     
GRID    51939           -7.404-940.     10.     
GRID    60545           -4.628-925.     50.     
GRID    60546           -1.851-910.     15.     
GRID    60547           -1.851-910.     7.5     
GRID    60548           -6.016-932.5    50.     
GRID    60549           -4.506-924.3440239.92526
GRID    60550           -3.369-918.2006920.81828
GRID    60551           -4.128-922.2998930.03937
GRID    60552           -5.746-931.0442929.05648
GRID    60553           -5.497-929.6963319.52379
GRID    60554           -5.506-929.7462 9.755176
GRID    60555           -5.879-931.7615739.31109
GRID    60556           -3.658-919.763979.467024
$==============================================================================$
CQUAD4  57622   1       173     123     60556   60554   
CQUAD4  57623   1       51937   51938   60553   60552   
CQUAD4  57624   1       60550   60551   60552   60553   
CQUAD4  57625   1       51939   60554   60553   51938   
CQUAD4  57626   1       60549   60545   60548   60555   
CQUAD4  57627   1       60550   60556   60547   60546   
CQUAD4  57628   1       60548   111     51936   60555   
CQUAD4  57629   1       60553   60554   60556   60550   
CQUAD4  57630   1       51939   101     173     60554   
CQUAD4  57631   1       60556   123     8       60547   
CQUAD4  57632   1       60551   60549   60555   60552   
CQUAD4  57633   1       60552   60555   51936   51937   
CQUAD4  100001  1       1       2       7       6       
CQUAD4  100002  1       2       3       8       7       
CQUAD4  100003  1       3       4       9       8       
CQUAD4  100004  1       4       5       10      9       
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
