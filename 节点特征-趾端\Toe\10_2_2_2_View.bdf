$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:05:51                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    11              -180.   -3.332-80.      
GRID    12              -190.   -3.517-80.      
GRID    13              -200.   -3.702-80.      
GRID    14              -210.   -3.887-80.      
GRID    15              -220.   -4.072-80.      
GRID    16              -180.   10.     0.      
GRID    17              -190.   10.     0.      
GRID    18              -200.   10.     0.      
GRID    19              -210.   10.     0.      
GRID    20              -220.   10.     0.      
GRID    102             -200.   60.     0.      
GRID    112             -200.   60.     70.     
GRID    128             -200.   20.     0.      
GRID    178             -200.   30.     0.      
GRID    227             -200.   40.     0.      
GRID    277             -200.   50.     0.      
GRID    51940           -200.   60.     60.     
GRID    51941           -200.   60.     50.     
GRID    51942           -200.   60.     40.     
GRID    51943           -200.   60.     30.     
GRID    51944           -200.   60.     20.     
GRID    51945           -200.   60.     10.     
GRID    60557           -200.   45.     70.     
GRID    60558           -200.   10.     15.     
GRID    60559           -200.   44.3787859.82797
GRID    60560           -200.   19.9216817.3105 
GRID    60561           -200.   28.2697923.15039
GRID    60562           -200.   34.6992531.05514
GRID    60563           -200.   39.3438 40.12539
GRID    60564           -200.   42.5218649.80776
GRID    60565           -200.   10.     7.5     
GRID    60566           -200.   52.5    70.     
GRID    60567           -200.   51.2379459.71303
GRID    60568           -200.   40.4101510.62908
GRID    60569           -200.   50.500439.399915
GRID    60570           -200.   51.8792118.74641
GRID    60571           -200.   19.002268.534843
GRID    60572           -200.   49.7727849.28801
GRID    60573           -200.   48.0147537.44856
GRID    60574           -200.   28.200049.721301
GRID    60575           -200.   40.3173326.4227 
GRID    60576           -200.   34.3730717.28417
GRID    60577           -200.   50.7005128.17791
GRID    60578           -200.   44.8068618.48744
$==============================================================================$
CTRIA3  57655   1       60574   60571   60560   
CTRIA3  57656   1       60577   60575   60573   
CTRIA3  57657   1       60574   60576   60568   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  57634   1       60573   60572   51941   51942   
CQUAD4  57635   1       51940   60567   60566   112     
CQUAD4  57636   1       60575   60577   60570   60578   
CQUAD4  57637   1       60563   60564   60572   60573   
CQUAD4  57638   1       60571   128     18      60565   
CQUAD4  57639   1       60559   60557   60566   60567   
CQUAD4  57640   1       60575   60578   60568   60576   
CQUAD4  57641   1       60570   51944   51945   60569   
CQUAD4  57642   1       60575   60562   60563   60573   
CQUAD4  57643   1       51944   60570   60577   51943   
CQUAD4  57644   1       60562   60575   60576   60561   
CQUAD4  57645   1       60570   60569   60568   60578   
CQUAD4  57646   1       60565   60558   60560   60571   
CQUAD4  57647   1       60574   60568   227     178     
CQUAD4  57648   1       60567   51940   51941   60572   
CQUAD4  57649   1       60564   60559   60567   60572   
CQUAD4  57650   1       60560   60561   60576   60574   
CQUAD4  57651   1       51942   51943   60577   60573   
CQUAD4  57652   1       51945   102     277     60569   
CQUAD4  57653   1       178     128     60571   60574   
CQUAD4  57654   1       277     227     60568   60569   
CQUAD4  100005  1       11      12      17      16      
CQUAD4  100006  1       12      13      18      17      
CQUAD4  100007  1       13      14      19      18      
CQUAD4  100008  1       14      15      20      19      
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
