$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:05:54                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16573           -400.   2010.   0.      
GRID    16583           -400.   2080.   0.      
GRID    16593           -400.   2080.   90.     
GRID    16649           -380.   2000.   0.      
GRID    16650           -390.   2000.   0.      
GRID    16651           -400.   2000.   0.      
GRID    16652           -410.   2000.   0.      
GRID    16653           -420.   2000.   0.      
GRID    16654           -380.   2010.   0.      
GRID    16655           -390.   2010.   0.      
GRID    16656           -410.   2010.   0.      
GRID    16657           -420.   2010.   0.      
GRID    16733           -400.   2020.   0.      
GRID    16783           -400.   2030.   0.      
GRID    16832           -400.   2040.   0.      
GRID    16882           -400.   2050.   0.      
GRID    16931           -400.   2060.   0.      
GRID    16981           -400.   2070.   0.      
GRID    49775           -400.   2080.   80.     
GRID    49776           -400.   2080.   70.     
GRID    49777           -400.   2080.   60.     
GRID    49778           -400.   2080.   50.     
GRID    49779           -400.   2080.   40.     
GRID    49780           -400.   2080.   30.     
GRID    49781           -400.   2080.   20.     
GRID    49782           -400.   2080.   10.     
GRID    61623           -400.   2065.   90.     
GRID    61624           -400.   2010.   15.     
GRID    61625           -400.   2010.   7.5     
GRID    61626           -400.   2072.5  90.     
GRID    61627           -400.   2064.43379.76173
GRID    61628           -400.   2020.16716.32685
GRID    61629           -400.   2029.72420.041  
GRID    61630           -400.   2038.26525.71497
GRID    61631           -400.   2045.61 32.86935
GRID    61632           -400.   2051.68541.12972
GRID    61633           -400.   2056.53850.16246
GRID    61634           -400.   2060.27459.71137
GRID    61635           -400.   2062.90169.62282
GRID    61636           -400.   2071.59428.59981
GRID    61637           -400.   2069.23919.40459
GRID    61638           -400.   2059.63 10.15585
GRID    61639           -400.   2058.55121.0608 
GRID    61640           -400.   2068.91959.35699
GRID    61641           -400.   2070.88669.4375 
GRID    61642           -400.   2032.62811.95781
GRID    61643           -400.   2021.1529.879757
GRID    61644           -400.   2066.89747.78049
GRID    61645           -400.   2049.10111.47578
GRID    61646           -400.   2059.62737.31957
GRID    61647           -400.   2051.98327.58093
GRID    61648           -400.   2043.09818.78671
GRID    61649           -400.   2070.19238.20431
GRID    61650           -400.   2071.94 79.70841
GRID    61651           -400.   2070.   10.     
GRID    61652           -400.   2063.50328.81465
GRID    61653           -400.   2040.5617.296929
$==============================================================================$
CTRIA3  58491   1       61646   61644   61649   
CTRIA3  58492   1       61639   61638   61645   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  58460   1       61637   61651   61638   61639   
CQUAD4  58461   1       61627   61623   61626   61650   
CQUAD4  58462   1       61645   61648   61647   61639   
CQUAD4  58463   1       61650   49775   49776   61641   
CQUAD4  58464   1       61630   61648   61642   61629   
CQUAD4  58465   1       61646   61649   61636   61652   
CQUAD4  58466   1       61631   61647   61648   61630   
CQUAD4  58467   1       49777   49778   61644   61640   
CQUAD4  58468   1       49779   61649   61644   49778   
CQUAD4  58469   1       61644   61646   61632   61633   
CQUAD4  58470   1       16981   61651   49782   16583   
CQUAD4  58471   1       61642   16783   16733   61643   
CQUAD4  58472   1       16832   16783   61642   61653   
CQUAD4  58473   1       61643   16733   16573   61625   
CQUAD4  58474   1       61626   16593   49775   61650   
CQUAD4  58475   1       61636   61637   61639   61652   
CQUAD4  58476   1       61633   61634   61640   61644   
CQUAD4  58477   1       61641   61640   61634   61635   
CQUAD4  58478   1       61645   16882   16832   61653   
CQUAD4  58479   1       49776   49777   61640   61641   
CQUAD4  58480   1       49779   49780   61636   61649   
CQUAD4  58481   1       61632   61646   61647   61631   
CQUAD4  58482   1       61653   61642   61648   61645   
CQUAD4  58483   1       61647   61646   61652   61639   
CQUAD4  58484   1       61629   61642   61643   61628   
CQUAD4  58485   1       49781   61637   61636   49780   
CQUAD4  58486   1       16981   16931   61638   61651   
CQUAD4  58487   1       16882   61645   61638   16931   
CQUAD4  58488   1       49782   61651   61637   49781   
CQUAD4  58489   1       61628   61643   61625   61624   
CQUAD4  58490   1       61635   61627   61650   61641   
CQUAD4  100049  1       16649   16650   16655   16654   
CQUAD4  100050  1       16650   16651   16573   16655   
CQUAD4  100051  1       16651   16652   16656   16573   
CQUAD4  100052  1       16652   16653   16657   16656   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
