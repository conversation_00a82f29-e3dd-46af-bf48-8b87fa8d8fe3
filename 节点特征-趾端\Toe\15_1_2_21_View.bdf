$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:38:40                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16601           -1.113-66015.   0.      
GRID    16611           -1.122-66060.   0.      
GRID    16621           -1.122-66060.   75.     
GRID    33171           30.     6000.   0.      
GRID    33172           15.     6000.   0.      
GRID    33173           -1.111-66000.   0.      
GRID    33174           -15.    6000.   0.      
GRID    33175           -30.    6000.   0.      
GRID    33176           30.     6015.   0.      
GRID    33177           15.     6015.   0.      
GRID    33178           -15.    6015.   0.      
GRID    33179           -30.    6015.   0.      
GRID    33263           -1.116-66030.   0.      
GRID    33313           -1.119-66045.   0.      
GRID    50299           -1.122-66060.   60.     
GRID    50300           -1.122-66060.   45.     
GRID    50301           -1.122-66060.   30.     
GRID    50302           -1.122-66060.   15.     
GRID    72236           -1.119-66045.   75.     
GRID    72237           -1.113-66015.   15.     
GRID    72238           -1.119-66044.09860.59318
GRID    72239           -1.116-66028.05821.07175
GRID    72240           -1.117-66036.33832.88671
GRID    72241           -1.118-66041.34146.4246 
GRID    72242           -1.119-66046.96728.83237
GRID    72243           -1.119-66044.12 16.07744
$==============================================================================$
CTRIA3  101505  1       72242   72240   72241   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100081  1       33171   33172   33177   33176   
CQUAD4  100082  1       33172   33173   16601   33177   
CQUAD4  100083  1       33173   33174   33178   16601   
CQUAD4  100084  1       33174   33175   33179   33178   
CQUAD4  101497  1       72243   72242   50301   50302   
CQUAD4  101498  1       72241   50300   50301   72242   
CQUAD4  101499  1       72238   72236   16621   50299   
CQUAD4  101500  1       72239   72243   33313   33263   
CQUAD4  101501  1       72239   72240   72242   72243   
CQUAD4  101502  1       72239   33263   16601   72237   
CQUAD4  101503  1       50299   50300   72241   72238   
CQUAD4  101504  1       50302   16611   33313   72243   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
