$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:38:35                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    41              -1170.  -2.166-70.      
GRID    42              -1185.  -2.193-70.      
GRID    43              -1200.  -2.221-70.      
GRID    44              -1215.  -2.249-70.      
GRID    45              -1230.  -2.277-70.      
GRID    46              -1170.  15.     0.      
GRID    47              -1185.  15.     0.      
GRID    48              -1200.  15.     0.      
GRID    49              -1215.  15.     0.      
GRID    50              -1230.  15.     0.      
GRID    105             -1200.  180.    0.      
GRID    115             -1200.  180.    180.    
GRID    143             -1200.  30.     0.      
GRID    193             -1200.  45.     0.      
GRID    242             -1200.  60.     0.      
GRID    292             -1200.  75.     0.      
GRID    341             -1200.  90.     0.      
GRID    391             -1200.  105.    0.      
GRID    440             -1200.  120.    0.      
GRID    490             -1200.  135.    0.      
GRID    539             -1200.  150.    0.      
GRID    589             -1200.  165.    0.      
GRID    51962           -1200.  180.    165.    
GRID    51963           -1200.  180.    150.    
GRID    51964           -1200.  180.    135.    
GRID    51965           -1200.  180.    120.    
GRID    51966           -1200.  180.    105.    
GRID    51967           -1200.  180.    90.     
GRID    51968           -1200.  180.    75.     
GRID    51969           -1200.  180.    60.     
GRID    51970           -1200.  180.    45.     
GRID    51971           -1200.  180.    30.     
GRID    51972           -1200.  180.    15.     
GRID    70477           -1200.  165.    180.    
GRID    70478           -1200.  15.     15.     
GRID    70479           -1200.  164.4096165.4588
GRID    70480           -1200.  29.5309515.80358
GRID    70481           -1200.  43.8968418.1306 
GRID    70482           -1200.  57.9415921.94351
GRID    70483           -1200.  71.5188227.18296
GRID    70484           -1200.  84.4902833.781  
GRID    70485           -1200.  96.7207541.66823
GRID    70486           -1200.  108.131150.70133
GRID    70487           -1200.  118.622560.78716
GRID    70488           -1200.  128.154471.78422
GRID    70489           -1200.  136.679983.5786 
GRID    70490           -1200.  144.092896.10227
GRID    70491           -1200.  150.4388109.1989
GRID    70492           -1200.  155.6579122.7839
GRID    70493           -1200.  159.733 136.7548
GRID    70494           -1200.  162.6527151.0121
GRID    70495           -1200.  153.568258.13178
GRID    70496           -1200.  121.822427.37471
GRID    70497           -1200.  148.717244.3185 
GRID    70498           -1200.  133.878330.38507
GRID    70499           -1200.  164.2727104.5034
GRID    70500           -1200.  75.4903615.9285 
GRID    70501           -1200.  165.85  120.4246
GRID    70502           -1200.  93.9860119.42911
GRID    70503           -1200.  160.427788.98135
GRID    70504           -1200.  147.891777.68674
GRID    70505           -1200.  139.516 62.77402
GRID    70506           -1200.  131.198247.1932 
GRID    70507           -1200.  116.734340.18838
GRID    70508           -1200.  103.796633.05079
GRID    70509           -1200.  121.345813.62104
GRID    70510           -1200.  107.615 14.09851
GRID    70511           -1200.  165.    15.     
GRID    70512           -1200.  166.298 74.65051
GRID    70513           -1200.  166.714558.56164
GRID    70514           -1200.  150.    15.     
GRID    70515           -1200.  135.326814.6743 
GRID    70516           -1200.  164.477843.60722
GRID    70517           -1200.  165.    30.     
GRID    70518           -1200.  149.358230.07149
GRID    70519           -1200.  155.863370.90848
GRID    70520           -1200.  111.099824.96382
$==============================================================================$
CTRIA3  100252  1       70500   70482   70483   
CTRIA3  100253  1       70501   70492   70493   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100017  1       41      42      47      46      
CQUAD4  100018  1       42      43      48      47      
CQUAD4  100019  1       43      44      49      48      
CQUAD4  100020  1       44      45      50      49      
CQUAD4  100207  1       70493   51964   51965   70501   
CQUAD4  100208  1       51969   51970   70516   70513   
CQUAD4  100209  1       70515   70514   539     490     
CQUAD4  100210  1       51972   70511   70517   51971   
CQUAD4  100211  1       70482   242     193     70481   
CQUAD4  100212  1       70483   70484   70502   70500   
CQUAD4  100213  1       391     341     70502   70510   
CQUAD4  100214  1       292     70500   70502   341     
CQUAD4  100215  1       440     391     70510   70509   
CQUAD4  100216  1       70517   70518   70497   70516   
CQUAD4  100217  1       70479   70477   115     51962   
CQUAD4  100218  1       70503   70504   70489   70490   
CQUAD4  100219  1       51968   70512   70503   51967   
CQUAD4  100220  1       70489   70504   70505   70488   
CQUAD4  100221  1       70513   70516   70497   70495   
CQUAD4  100222  1       490     440     70509   70515   
CQUAD4  100223  1       51965   51966   70499   70501   
CQUAD4  100224  1       70506   70497   70518   70498   
CQUAD4  100225  1       70508   70485   70486   70507   
CQUAD4  100226  1       70480   143     48      70478   
CQUAD4  100227  1       70510   70520   70496   70509   
CQUAD4  100228  1       589     539     70514   70511   
CQUAD4  100229  1       51970   51971   70517   70516   
CQUAD4  100230  1       70492   70501   70499   70491   
CQUAD4  100231  1       70482   70500   292     242     
CQUAD4  100232  1       70495   70505   70504   70519   
CQUAD4  100233  1       143     70480   70481   193     
CQUAD4  100234  1       70506   70487   70488   70505   
CQUAD4  100235  1       70487   70506   70507   70486   
CQUAD4  100236  1       70506   70498   70496   70507   
CQUAD4  100237  1       70504   70503   70512   70519   
CQUAD4  100238  1       70509   70496   70498   70515   
CQUAD4  100239  1       589     70511   51972   105     
CQUAD4  100240  1       70493   70494   51963   51964   
CQUAD4  100241  1       70491   70499   70503   70490   
CQUAD4  100242  1       70485   70508   70502   70484   
CQUAD4  100243  1       70510   70502   70508   70520   
CQUAD4  100244  1       51962   51963   70494   70479   
CQUAD4  100245  1       51968   51969   70513   70512   
CQUAD4  100246  1       51966   51967   70503   70499   
CQUAD4  100247  1       70495   70519   70512   70513   
CQUAD4  100248  1       70517   70511   70514   70518   
CQUAD4  100249  1       70508   70507   70496   70520   
CQUAD4  100250  1       70506   70505   70495   70497   
CQUAD4  100251  1       70515   70498   70518   70514   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
