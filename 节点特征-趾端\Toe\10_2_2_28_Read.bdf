$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:05:59                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16608           -1400.  4010.   0.      
GRID    33234           -1380.  4000.   0.      
GRID    33235           -1390.  4000.   0.      
GRID    33236           -1400.  4000.   0.      
GRID    33237           -1410.  4000.   0.      
GRID    33238           -1420.  4000.   0.      
GRID    33239           -1380.  4010.   0.      
GRID    33240           -1390.  4010.   0.      
GRID    33241           -1410.  4010.   0.      
GRID    33242           -1420.  4010.   0.      
$==============================================================================$
CQUAD4  100109  1       33234   33235   33240   33239   
CQUAD4  100110  1       33235   33236   16608   33240   
CQUAD4  100111  1       33236   33237   33241   16608   
CQUAD4  100112  1       33237   33238   33242   33241   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
