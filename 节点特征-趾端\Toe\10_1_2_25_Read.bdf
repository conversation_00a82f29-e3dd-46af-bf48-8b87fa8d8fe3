$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:03:45                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16605           -800.   4010.   0.      
GRID    33207           -780.   4000.   0.      
GRID    33208           -790.   4000.   0.      
GRID    33209           -800.   4000.   0.      
GRID    33210           -810.   4000.   0.      
GRID    33211           -820.   4000.   0.      
GRID    33212           -780.   4010.   0.      
GRID    33213           -790.   4010.   0.      
GRID    33214           -810.   4010.   0.      
GRID    33215           -820.   4010.   0.      
$==============================================================================$
CQUAD4  100097  1       33207   33208   33213   33212   
CQUAD4  100098  1       33208   33209   16605   33213   
CQUAD4  100099  1       33209   33210   33214   16605   
CQUAD4  100100  1       33210   33211   33215   33214   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
