$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:38:41                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16605           -1200.  6015.   0.      
GRID    16615           -1200.  6180.   0.      
GRID    16625           -1200.  6180.   180.    
GRID    33207           -1170.  6000.   0.      
GRID    33208           -1185.  6000.   0.      
GRID    33209           -1200.  6000.   0.      
GRID    33210           -1215.  6000.   0.      
GRID    33211           -1230.  6000.   0.      
GRID    33212           -1170.  6015.   0.      
GRID    33213           -1185.  6015.   0.      
GRID    33214           -1215.  6015.   0.      
GRID    33215           -1230.  6015.   0.      
GRID    33283           -1200.  6030.   0.      
GRID    33333           -1200.  6045.   0.      
GRID    33382           -1200.  6060.   0.      
GRID    33432           -1200.  6075.   0.      
GRID    33481           -1200.  6090.   0.      
GRID    33531           -1200.  6105.   0.      
GRID    33580           -1200.  6120.   0.      
GRID    33630           -1200.  6135.   0.      
GRID    33679           -1200.  6150.   0.      
GRID    33729           -1200.  6165.   0.      
GRID    50621           -1200.  6180.   165.    
GRID    50622           -1200.  6180.   150.    
GRID    50623           -1200.  6180.   135.    
GRID    50624           -1200.  6180.   120.    
GRID    50625           -1200.  6180.   105.    
GRID    50626           -1200.  6180.   90.     
GRID    50627           -1200.  6180.   75.     
GRID    50628           -1200.  6180.   60.     
GRID    50629           -1200.  6180.   45.     
GRID    50630           -1200.  6180.   30.     
GRID    50631           -1200.  6180.   15.     
GRID    72313           -1200.  6165.   180.    
GRID    72314           -1200.  6015.   15.     
GRID    72315           -1200.  6164.41 165.4588
GRID    72316           -1200.  6029.53115.80358
GRID    72317           -1200.  6043.89718.1306 
GRID    72318           -1200.  6057.94221.94351
GRID    72319           -1200.  6071.51927.18296
GRID    72320           -1200.  6084.49 33.781  
GRID    72321           -1200.  6096.72141.66823
GRID    72322           -1200.  6108.13150.70133
GRID    72323           -1200.  6118.62260.78716
GRID    72324           -1200.  6128.15471.78422
GRID    72325           -1200.  6136.68 83.5786 
GRID    72326           -1200.  6144.09396.10227
GRID    72327           -1200.  6150.439109.1989
GRID    72328           -1200.  6155.658122.7839
GRID    72329           -1200.  6159.733136.7548
GRID    72330           -1200.  6162.653151.0121
GRID    72331           -1200.  6153.56858.13178
GRID    72332           -1200.  6121.82227.37471
GRID    72333           -1200.  6148.71744.3185 
GRID    72334           -1200.  6133.87830.38507
GRID    72335           -1200.  6164.273104.5034
GRID    72336           -1200.  6075.49 15.9285 
GRID    72337           -1200.  6165.85 120.4246
GRID    72338           -1200.  6093.98619.42911
GRID    72339           -1200.  6160.42888.98135
GRID    72340           -1200.  6147.89277.68674
GRID    72341           -1200.  6139.51662.77402
GRID    72342           -1200.  6131.19847.1932 
GRID    72343           -1200.  6116.73440.18838
GRID    72344           -1200.  6103.79733.05079
GRID    72345           -1200.  6121.34613.62104
GRID    72346           -1200.  6107.61514.09851
GRID    72347           -1200.  6165.   15.     
GRID    72348           -1200.  6166.29874.65051
GRID    72349           -1200.  6166.71558.56164
GRID    72350           -1200.  6150.   15.     
GRID    72351           -1200.  6135.32714.6743 
GRID    72352           -1200.  6164.47843.60722
GRID    72353           -1200.  6165.   30.     
GRID    72354           -1200.  6149.35830.07149
GRID    72355           -1200.  6155.86370.90848
GRID    72356           -1200.  6111.1  24.96382
$==============================================================================$
CTRIA3  101628  1       72336   72318   72319   
CTRIA3  101629  1       72337   72328   72329   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100097  1       33207   33208   33213   33212   
CQUAD4  100098  1       33208   33209   16605   33213   
CQUAD4  100099  1       33209   33210   33214   16605   
CQUAD4  100100  1       33210   33211   33215   33214   
CQUAD4  101583  1       72329   50623   50624   72337   
CQUAD4  101584  1       50628   50629   72352   72349   
CQUAD4  101585  1       72351   72350   33679   33630   
CQUAD4  101586  1       50631   72347   72353   50630   
CQUAD4  101587  1       72318   33382   33333   72317   
CQUAD4  101588  1       72319   72320   72338   72336   
CQUAD4  101589  1       33531   33481   72338   72346   
CQUAD4  101590  1       33432   72336   72338   33481   
CQUAD4  101591  1       33580   33531   72346   72345   
CQUAD4  101592  1       72353   72354   72333   72352   
CQUAD4  101593  1       72315   72313   16625   50621   
CQUAD4  101594  1       72339   72340   72325   72326   
CQUAD4  101595  1       50627   72348   72339   50626   
CQUAD4  101596  1       72325   72340   72341   72324   
CQUAD4  101597  1       72349   72352   72333   72331   
CQUAD4  101598  1       33630   33580   72345   72351   
CQUAD4  101599  1       50624   50625   72335   72337   
CQUAD4  101600  1       72342   72333   72354   72334   
CQUAD4  101601  1       72344   72321   72322   72343   
CQUAD4  101602  1       72316   33283   16605   72314   
CQUAD4  101603  1       72346   72356   72332   72345   
CQUAD4  101604  1       33729   33679   72350   72347   
CQUAD4  101605  1       50629   50630   72353   72352   
CQUAD4  101606  1       72328   72337   72335   72327   
CQUAD4  101607  1       72318   72336   33432   33382   
CQUAD4  101608  1       72331   72341   72340   72355   
CQUAD4  101609  1       33283   72316   72317   33333   
CQUAD4  101610  1       72342   72323   72324   72341   
CQUAD4  101611  1       72323   72342   72343   72322   
CQUAD4  101612  1       72342   72334   72332   72343   
CQUAD4  101613  1       72340   72339   72348   72355   
CQUAD4  101614  1       72345   72332   72334   72351   
CQUAD4  101615  1       33729   72347   50631   16615   
CQUAD4  101616  1       72329   72330   50622   50623   
CQUAD4  101617  1       72327   72335   72339   72326   
CQUAD4  101618  1       72321   72344   72338   72320   
CQUAD4  101619  1       72346   72338   72344   72356   
CQUAD4  101620  1       50621   50622   72330   72315   
CQUAD4  101621  1       50627   50628   72349   72348   
CQUAD4  101622  1       50625   50626   72339   72335   
CQUAD4  101623  1       72331   72355   72348   72349   
CQUAD4  101624  1       72353   72347   72350   72354   
CQUAD4  101625  1       72344   72343   72332   72356   
CQUAD4  101626  1       72342   72341   72331   72333   
CQUAD4  101627  1       72351   72334   72354   72350   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
