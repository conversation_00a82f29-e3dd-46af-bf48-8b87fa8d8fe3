$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:38:34                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    21              -570.   -1.055-70.      
GRID    22              -585.   -1.083-70.      
GRID    23              -600.   -1.111-70.      
GRID    24              -615.   -1.138-70.      
GRID    25              -630.   -1.166-70.      
GRID    26              -570.   15.     0.      
GRID    27              -585.   15.     0.      
GRID    28              -600.   15.     0.      
GRID    29              -615.   15.     0.      
GRID    30              -630.   15.     0.      
$==============================================================================$
CQUAD4  100009  1       21      22      27      26      
CQUAD4  100010  1       22      23      28      27      
CQUAD4  100011  1       23      24      29      28      
CQUAD4  100012  1       24      25      30      29      
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
