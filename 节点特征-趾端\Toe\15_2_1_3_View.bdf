$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:39:01                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    21              0.      570.    0.      
GRID    22              0.      585.    0.      
GRID    23              0.      600.    0.      
GRID    24              0.      615.    0.      
GRID    25              0.      630.    0.      
GRID    26              15.     570.    0.      
GRID    27              15.     585.    0.      
GRID    28              15.     600.    0.      
GRID    29              15.     615.    0.      
GRID    30              15.     630.    0.      
GRID    103             120.    600.    0.      
GRID    113             120.    600.    135.    
GRID    133             30.     600.    0.      
GRID    183             45.     600.    0.      
GRID    232             60.     600.    0.      
GRID    282             75.     600.    0.      
GRID    331             90.     600.    0.      
GRID    381             105.    600.    0.      
GRID    51946           120.    600.    120.    
GRID    51947           120.    600.    105.    
GRID    51948           120.    600.    90.     
GRID    51949           120.    600.    75.     
GRID    51950           120.    600.    60.     
GRID    51951           120.    600.    45.     
GRID    51952           120.    600.    30.     
GRID    51953           120.    600.    15.     
GRID    70423           105.    600.    135.    
GRID    70424           15.     600.    15.     
GRID    70425           104.2059600.    119.972 
GRID    70426           29.95184600.    16.70561
GRID    70427           44.192  600.    21.57155
GRID    70428           57.20001600.    29.13774
GRID    70429           68.70547600.    38.83722
GRID    70430           78.63274600.    50.14682
GRID    70431           86.82308600.    62.77149
GRID    70432           93.44324600.    76.28589
GRID    70433           98.48573600.    90.46482
GRID    70434           102.1201600.    105.0683
GRID    70435           105.4111600.    14.51462
GRID    70436           90.84165600.    14.11553
GRID    70437           76.04817600.    14.70329
GRID    70438           91.69085600.    28.27512
GRID    70439           104.3036600.    73.94908
GRID    70440           63.26769600.    18.15918
GRID    70441           100.7487600.    57.51653
GRID    70442           46.23255600.    13.26835
GRID    70443           88.22371600.    42.08906
GRID    70444           80.05542600.    26.08506
GRID    70445           104.7256600.    43.3857 
GRID    70446           105.3347600.    29.43759
GRID    70447           74.2317 600.    32.55355
$==============================================================================$
CTRIA3  100172  1       70445   70443   70441   
CTRIA3  100173  1       70442   70426   70427   
CTRIA3  100174  1       70439   70432   70433   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100009  1       21      22      27      26      
CQUAD4  100010  1       22      23      28      27      
CQUAD4  100011  1       23      24      29      28      
CQUAD4  100012  1       24      25      30      29      
CQUAD4  100147  1       70435   70446   51952   51953   
CQUAD4  100148  1       331     282     70437   70436   
CQUAD4  100149  1       70439   70441   70431   70432   
CQUAD4  100150  1       51952   70446   70445   51951   
CQUAD4  100151  1       70426   133     28      70424   
CQUAD4  100152  1       331     70436   70435   381     
CQUAD4  100153  1       70425   70423   113     51946   
CQUAD4  100154  1       70438   70446   70435   70436   
CQUAD4  100155  1       51947   51948   70433   70434   
CQUAD4  100156  1       70429   70430   70443   70447   
CQUAD4  100157  1       70438   70436   70437   70444   
CQUAD4  100158  1       183     70442   70440   232     
CQUAD4  100159  1       70444   70437   70440   70447   
CQUAD4  100160  1       70440   70428   70429   70447   
CQUAD4  100161  1       70430   70431   70441   70443   
CQUAD4  100162  1       183     133     70426   70442   
CQUAD4  100163  1       381     70435   51953   103     
CQUAD4  100164  1       51949   51950   70441   70439   
CQUAD4  100165  1       70445   70446   70438   70443   
CQUAD4  100166  1       51951   70445   70441   51950   
CQUAD4  100167  1       51946   51947   70434   70425   
CQUAD4  100168  1       51949   70439   70433   51948   
CQUAD4  100169  1       70443   70438   70444   70447   
CQUAD4  100170  1       70428   70440   70442   70427   
CQUAD4  100171  1       232     70440   70437   282     
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
