$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:39:05                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16574           3015.   900.    0.      
GRID    16584           3150.   900.    0.      
GRID    16594           3150.   900.    135.    
GRID    16658           3000.   870.    0.      
GRID    16659           3000.   885.    0.      
GRID    16660           3000.   900.    0.      
GRID    16661           3000.   915.    0.      
GRID    16662           3000.   930.    0.      
GRID    16663           3015.   870.    0.      
GRID    16664           3015.   885.    0.      
GRID    16665           3015.   915.    0.      
GRID    16666           3015.   930.    0.      
GRID    16738           3030.   900.    0.      
GRID    16788           3045.   900.    0.      
GRID    16837           3060.   900.    0.      
GRID    16887           3075.   900.    0.      
GRID    16936           3090.   900.    0.      
GRID    16986           3105.   900.    0.      
GRID    17035           3120.   900.    0.      
GRID    17085           3135.   900.    0.      
GRID    49809           3150.   900.    120.    
GRID    49810           3150.   900.    105.    
GRID    49811           3150.   900.    90.     
GRID    49812           3150.   900.    75.     
GRID    49813           3150.   900.    60.     
GRID    49814           3150.   900.    45.     
GRID    49815           3150.   900.    30.     
GRID    49816           3150.   900.    15.     
GRID    71366           3135.   900.    135.    
GRID    71367           3015.   900.    15.     
GRID    71368           3134.018900.    120.5509
GRID    71369           3029.449900.    15.98209
GRID    71370           3043.699900.    18.56594
GRID    71371           3057.532900.    22.85408
GRID    71372           3070.717900.    28.84726
GRID    71373           3083.152900.    36.27047
GRID    71374           3094.534900.    45.22556
GRID    71375           3104.774900.    55.46622
GRID    71376           3113.73 900.    66.84815
GRID    71377           3121.153900.    79.28349
GRID    71378           3127.146900.    92.4677 
GRID    71379           3131.434900.    106.3008
GRID    71380           3105.898900.    15.15145
GRID    71381           3135.679900.    43.32279
GRID    71382           3135.329900.    29.10375
GRID    71383           3120.821900.    27.40962
GRID    71384           3075.536900.    15.02695
GRID    71385           3134.49 900.    73.79722
GRID    71386           3061.347900.    12.14523
GRID    71387           3137.855900.    88.65264
GRID    71388           3091.132900.    19.16893
GRID    71389           3129.933900.    57.46477
GRID    71390           3106.238900.    30.49331
GRID    71391           3115.677900.    46.48269
GRID    71392           3135.291900.    14.54575
GRID    71393           3120.65 900.    13.7791 
GRID    71394           3123.468900.    39.35618
$==============================================================================$
CTRIA3  100892  1       71386   71370   71371   
CTRIA3  100893  1       71387   71378   71379   
CTRIA3  100894  1       71388   71390   71380   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100053  1       16658   16659   16664   16663   
CQUAD4  100054  1       16659   16660   16574   16664   
CQUAD4  100055  1       16660   16661   16665   16574   
CQUAD4  100056  1       16661   16662   16666   16665   
CQUAD4  100863  1       16837   71386   71384   16887   
CQUAD4  100864  1       71378   71387   71385   71377   
CQUAD4  100865  1       71383   71390   71391   71394   
CQUAD4  100866  1       16574   71367   71369   16738   
CQUAD4  100867  1       49809   49810   71379   71368   
CQUAD4  100868  1       71388   71380   16986   16936   
CQUAD4  100869  1       71370   16788   16738   71369   
CQUAD4  100870  1       71385   71387   49811   49812   
CQUAD4  100871  1       49815   71382   71381   49814   
CQUAD4  100872  1       71382   71383   71394   71381   
CQUAD4  100873  1       71389   71385   49812   49813   
CQUAD4  100874  1       17035   16986   71380   71393   
CQUAD4  100875  1       71393   71383   71382   71392   
CQUAD4  100876  1       71388   71373   71374   71390   
CQUAD4  100877  1       71376   71377   71385   71389   
CQUAD4  100878  1       71384   71372   71373   71388   
CQUAD4  100879  1       71371   71372   71384   71386   
CQUAD4  100880  1       71390   71383   71393   71380   
CQUAD4  100881  1       49816   16584   17085   71392   
CQUAD4  100882  1       71391   71389   71381   71394   
CQUAD4  100883  1       49814   71381   71389   49813   
CQUAD4  100884  1       71370   71386   16837   16788   
CQUAD4  100885  1       49815   49816   71392   71382   
CQUAD4  100886  1       71375   71376   71389   71391   
CQUAD4  100887  1       71375   71391   71390   71374   
CQUAD4  100888  1       17035   71393   71392   17085   
CQUAD4  100889  1       49811   71387   71379   49810   
CQUAD4  100890  1       71368   71366   16594   49809   
CQUAD4  100891  1       16887   71384   71388   16936   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
