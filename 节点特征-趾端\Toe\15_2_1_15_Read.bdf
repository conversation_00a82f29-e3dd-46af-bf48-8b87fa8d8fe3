$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:39:05                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16575           3015.   1200.   0.      
GRID    16667           3000.   1170.   0.      
GRID    16668           3000.   1185.   0.      
GRID    16669           3000.   1200.   0.      
GRID    16670           3000.   1215.   0.      
GRID    16671           3000.   1230.   0.      
GRID    16672           3015.   1170.   0.      
GRID    16673           3015.   1185.   0.      
GRID    16674           3015.   1215.   0.      
GRID    16675           3015.   1230.   0.      
$==============================================================================$
CQUAD4  100057  1       16667   16668   16673   16672   
CQUAD4  100058  1       16668   16669   16575   16673   
CQUAD4  100059  1       16669   16670   16674   16575   
CQUAD4  100060  1       16670   16671   16675   16674   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
