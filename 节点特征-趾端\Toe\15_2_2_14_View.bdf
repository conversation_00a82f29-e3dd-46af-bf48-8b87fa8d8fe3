$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:39:32                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16574           -900.   3015.   0.      
GRID    16584           -900.   3150.   0.      
GRID    16594           -900.   3150.   135.    
GRID    16658           -870.   3000.   0.      
GRID    16659           -885.   3000.   0.      
GRID    16660           -900.   3000.   0.      
GRID    16661           -915.   3000.   0.      
GRID    16662           -930.   3000.   0.      
GRID    16663           -870.   3015.   0.      
GRID    16664           -885.   3015.   0.      
GRID    16665           -915.   3015.   0.      
GRID    16666           -930.   3015.   0.      
GRID    16738           -900.   3030.   0.      
GRID    16788           -900.   3045.   0.      
GRID    16837           -900.   3060.   0.      
GRID    16887           -900.   3075.   0.      
GRID    16936           -900.   3090.   0.      
GRID    16986           -900.   3105.   0.      
GRID    17035           -900.   3120.   0.      
GRID    17085           -900.   3135.   0.      
GRID    49809           -900.   3150.   120.    
GRID    49810           -900.   3150.   105.    
GRID    49811           -900.   3150.   90.     
GRID    49812           -900.   3150.   75.     
GRID    49813           -900.   3150.   60.     
GRID    49814           -900.   3150.   45.     
GRID    49815           -900.   3150.   30.     
GRID    49816           -900.   3150.   15.     
GRID    71366           -900.   3135.   135.    
GRID    71367           -900.   3015.   15.     
GRID    71368           -900.   3134.018120.5509
GRID    71369           -900.   3029.44915.98209
GRID    71370           -900.   3043.69918.56594
GRID    71371           -900.   3057.53222.85408
GRID    71372           -900.   3070.71728.84726
GRID    71373           -900.   3083.15236.27047
GRID    71374           -900.   3094.53445.22556
GRID    71375           -900.   3104.77455.46622
GRID    71376           -900.   3113.73 66.84815
GRID    71377           -900.   3121.15379.28349
GRID    71378           -900.   3127.14692.4677 
GRID    71379           -900.   3131.434106.3008
GRID    71380           -900.   3105.89815.15145
GRID    71381           -900.   3135.67943.32279
GRID    71382           -900.   3135.32929.10375
GRID    71383           -900.   3120.82127.40962
GRID    71384           -900.   3075.53615.02695
GRID    71385           -900.   3134.49 73.79722
GRID    71386           -900.   3061.34712.14523
GRID    71387           -900.   3137.85588.65264
GRID    71388           -900.   3091.13219.16893
GRID    71389           -900.   3129.93357.46477
GRID    71390           -900.   3106.23830.49331
GRID    71391           -900.   3115.67746.48269
GRID    71392           -900.   3135.29114.54575
GRID    71393           -900.   3120.65 13.7791 
GRID    71394           -900.   3123.46839.35618
$==============================================================================$
CTRIA3  100892  1       71386   71370   71371   
CTRIA3  100893  1       71387   71378   71379   
CTRIA3  100894  1       71388   71390   71380   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100053  1       16658   16659   16664   16663   
CQUAD4  100054  1       16659   16660   16574   16664   
CQUAD4  100055  1       16660   16661   16665   16574   
CQUAD4  100056  1       16661   16662   16666   16665   
CQUAD4  100863  1       16837   71386   71384   16887   
CQUAD4  100864  1       71378   71387   71385   71377   
CQUAD4  100865  1       71383   71390   71391   71394   
CQUAD4  100866  1       16574   71367   71369   16738   
CQUAD4  100867  1       49809   49810   71379   71368   
CQUAD4  100868  1       71388   71380   16986   16936   
CQUAD4  100869  1       71370   16788   16738   71369   
CQUAD4  100870  1       71385   71387   49811   49812   
CQUAD4  100871  1       49815   71382   71381   49814   
CQUAD4  100872  1       71382   71383   71394   71381   
CQUAD4  100873  1       71389   71385   49812   49813   
CQUAD4  100874  1       17035   16986   71380   71393   
CQUAD4  100875  1       71393   71383   71382   71392   
CQUAD4  100876  1       71388   71373   71374   71390   
CQUAD4  100877  1       71376   71377   71385   71389   
CQUAD4  100878  1       71384   71372   71373   71388   
CQUAD4  100879  1       71371   71372   71384   71386   
CQUAD4  100880  1       71390   71383   71393   71380   
CQUAD4  100881  1       49816   16584   17085   71392   
CQUAD4  100882  1       71391   71389   71381   71394   
CQUAD4  100883  1       49814   71381   71389   49813   
CQUAD4  100884  1       71370   71386   16837   16788   
CQUAD4  100885  1       49815   49816   71392   71382   
CQUAD4  100886  1       71375   71376   71389   71391   
CQUAD4  100887  1       71375   71391   71390   71374   
CQUAD4  100888  1       17035   71393   71392   17085   
CQUAD4  100889  1       49811   71387   71379   49810   
CQUAD4  100890  1       71368   71366   16594   49809   
CQUAD4  100891  1       16887   71384   71388   16936   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
