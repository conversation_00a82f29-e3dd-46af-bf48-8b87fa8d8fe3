$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:38:34                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    11              -270.   -4.998-80.      
GRID    12              -285.   -5.275-80.      
GRID    13              -300.   -5.553-80.      
GRID    14              -315.   -5.831-80.      
GRID    15              -330.   -6.108-80.      
GRID    16              -270.   15.     0.      
GRID    17              -285.   15.     0.      
GRID    18              -300.   15.     0.      
GRID    19              -315.   15.     0.      
GRID    20              -330.   15.     0.      
GRID    102             -300.   90.     0.      
GRID    112             -300.   90.     105.    
GRID    128             -300.   30.     0.      
GRID    178             -300.   45.     0.      
GRID    227             -300.   60.     0.      
GRID    277             -300.   75.     0.      
GRID    51940           -300.   90.     90.     
GRID    51941           -300.   90.     75.     
GRID    51942           -300.   90.     60.     
GRID    51943           -300.   90.     45.     
GRID    51944           -300.   90.     30.     
GRID    51945           -300.   90.     15.     
GRID    70408           -300.   75.     105.    
GRID    70409           -300.   15.     15.     
GRID    70410           -300.   74.1591590.18579
GRID    70411           -300.   29.5766517.75945
GRID    70412           -300.   42.4714 25.09506
GRID    70413           -300.   53.0588735.48817
GRID    70414           -300.   61.3033747.82362
GRID    70415           -300.   67.4235261.34004
GRID    70416           -300.   71.6617175.55959
GRID    70417           -300.   77.3656659.02478
GRID    70418           -300.   72.4841132.50837
GRID    70419           -300.   58.4344218.00521
GRID    70420           -300.   73.1737516.89507
GRID    70421           -300.   75.2017345.69008
GRID    70422           -300.   45.6911314.46414
$==============================================================================$
CTRIA3  100145  1       70417   70415   70416   
CTRIA3  100146  1       70422   70411   70412   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100005  1       11      12      17      16      
CQUAD4  100006  1       12      13      18      17      
CQUAD4  100007  1       13      14      19      18      
CQUAD4  100008  1       14      15      20      19      
CQUAD4  100130  1       51942   70417   70416   51941   
CQUAD4  100131  1       51943   51944   70418   70421   
CQUAD4  100132  1       70422   70412   70413   70419   
CQUAD4  100133  1       70413   70414   70421   70418   
CQUAD4  100134  1       178     128     70411   70422   
CQUAD4  100135  1       51940   51941   70416   70410   
CQUAD4  100136  1       70415   70417   70421   70414   
CQUAD4  100137  1       70419   70420   277     227     
CQUAD4  100138  1       51942   51943   70421   70417   
CQUAD4  100139  1       70419   227     178     70422   
CQUAD4  100140  1       70410   70408   112     51940   
CQUAD4  100141  1       70420   70419   70413   70418   
CQUAD4  100142  1       51945   102     277     70420   
CQUAD4  100143  1       70418   51944   51945   70420   
CQUAD4  100144  1       70411   128     18      70409   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
