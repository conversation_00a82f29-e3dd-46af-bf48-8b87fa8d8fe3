$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:38:35                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    31              -870.   -1.61-7 0.      
GRID    32              -885.   -1.638-70.      
GRID    33              -900.   -1.666-70.      
GRID    34              -915.   -1.694-70.      
GRID    35              -930.   -1.721-70.      
GRID    36              -870.   15.     0.      
GRID    37              -885.   15.     0.      
GRID    38              -900.   15.     0.      
GRID    39              -915.   15.     0.      
GRID    40              -930.   15.     0.      
GRID    104             -900.   150.    0.      
GRID    114             -900.   150.    135.    
GRID    138             -900.   30.     0.      
GRID    188             -900.   45.     0.      
GRID    237             -900.   60.     0.      
GRID    287             -900.   75.     0.      
GRID    336             -900.   90.     0.      
GRID    386             -900.   105.    0.      
GRID    435             -900.   120.    0.      
GRID    485             -900.   135.    0.      
GRID    51954           -900.   150.    120.    
GRID    51955           -900.   150.    105.    
GRID    51956           -900.   150.    90.     
GRID    51957           -900.   150.    75.     
GRID    51958           -900.   150.    60.     
GRID    51959           -900.   150.    45.     
GRID    51960           -900.   150.    30.     
GRID    51961           -900.   150.    15.     
GRID    70448           -900.   135.    135.    
GRID    70449           -900.   15.     15.     
GRID    70450           -900.   134.0179120.5509
GRID    70451           -900.   29.4491415.98209
GRID    70452           -900.   43.6992518.56594
GRID    70453           -900.   57.5323 22.85408
GRID    70454           -900.   70.7165128.84726
GRID    70455           -900.   83.1518536.27047
GRID    70456           -900.   94.5337845.22556
GRID    70457           -900.   104.774455.46622
GRID    70458           -900.   113.729566.84815
GRID    70459           -900.   121.152779.28349
GRID    70460           -900.   127.145992.4677 
GRID    70461           -900.   131.4341106.3008
GRID    70462           -900.   105.898315.15145
GRID    70463           -900.   135.678843.32279
GRID    70464           -900.   135.328929.10375
GRID    70465           -900.   120.820727.40962
GRID    70466           -900.   75.5355515.02695
GRID    70467           -900.   134.489873.79722
GRID    70468           -900.   61.3473612.14523
GRID    70469           -900.   137.854888.65264
GRID    70470           -900.   91.1318919.16893
GRID    70471           -900.   129.932757.46477
GRID    70472           -900.   106.238130.49331
GRID    70473           -900.   115.676946.48269
GRID    70474           -900.   135.290714.54575
GRID    70475           -900.   120.649613.7791 
GRID    70476           -900.   123.467739.35618
$==============================================================================$
CTRIA3  100204  1       70468   70452   70453   
CTRIA3  100205  1       70469   70460   70461   
CTRIA3  100206  1       70470   70472   70462   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100013  1       31      32      37      36      
CQUAD4  100014  1       32      33      38      37      
CQUAD4  100015  1       33      34      39      38      
CQUAD4  100016  1       34      35      40      39      
CQUAD4  100175  1       237     70468   70466   287     
CQUAD4  100176  1       70460   70469   70467   70459   
CQUAD4  100177  1       70465   70472   70473   70476   
CQUAD4  100178  1       38      70449   70451   138     
CQUAD4  100179  1       51954   51955   70461   70450   
CQUAD4  100180  1       70470   70462   386     336     
CQUAD4  100181  1       70452   188     138     70451   
CQUAD4  100182  1       70467   70469   51956   51957   
CQUAD4  100183  1       51960   70464   70463   51959   
CQUAD4  100184  1       70464   70465   70476   70463   
CQUAD4  100185  1       70471   70467   51957   51958   
CQUAD4  100186  1       435     386     70462   70475   
CQUAD4  100187  1       70475   70465   70464   70474   
CQUAD4  100188  1       70470   70455   70456   70472   
CQUAD4  100189  1       70458   70459   70467   70471   
CQUAD4  100190  1       70466   70454   70455   70470   
CQUAD4  100191  1       70453   70454   70466   70468   
CQUAD4  100192  1       70472   70465   70475   70462   
CQUAD4  100193  1       51961   104     485     70474   
CQUAD4  100194  1       70473   70471   70463   70476   
CQUAD4  100195  1       51959   70463   70471   51958   
CQUAD4  100196  1       70452   70468   237     188     
CQUAD4  100197  1       51960   51961   70474   70464   
CQUAD4  100198  1       70457   70458   70471   70473   
CQUAD4  100199  1       70457   70473   70472   70456   
CQUAD4  100200  1       435     70475   70474   485     
CQUAD4  100201  1       51956   70469   70461   51955   
CQUAD4  100202  1       70450   70448   114     51954   
CQUAD4  100203  1       287     70466   70470   336     
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
