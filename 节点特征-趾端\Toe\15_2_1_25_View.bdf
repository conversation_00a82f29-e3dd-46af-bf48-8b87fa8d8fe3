$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:39:08                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16605           6015.   1200.   0.      
GRID    16615           6180.   1200.   0.      
GRID    16625           6180.   1200.   180.    
GRID    33207           6000.   1170.   0.      
GRID    33208           6000.   1185.   0.      
GRID    33209           6000.   1200.   0.      
GRID    33210           6000.   1215.   0.      
GRID    33211           6000.   1230.   0.      
GRID    33212           6015.   1170.   0.      
GRID    33213           6015.   1185.   0.      
GRID    33214           6015.   1215.   0.      
GRID    33215           6015.   1230.   0.      
GRID    33283           6030.   1200.   0.      
GRID    33333           6045.   1200.   0.      
GRID    33382           6060.   1200.   0.      
GRID    33432           6075.   1200.   0.      
GRID    33481           6090.   1200.   0.      
GRID    33531           6105.   1200.   0.      
GRID    33580           6120.   1200.   0.      
GRID    33630           6135.   1200.   0.      
GRID    33679           6150.   1200.   0.      
GRID    33729           6165.   1200.   0.      
GRID    50621           6180.   1200.   165.    
GRID    50622           6180.   1200.   150.    
GRID    50623           6180.   1200.   135.    
GRID    50624           6180.   1200.   120.    
GRID    50625           6180.   1200.   105.    
GRID    50626           6180.   1200.   90.     
GRID    50627           6180.   1200.   75.     
GRID    50628           6180.   1200.   60.     
GRID    50629           6180.   1200.   45.     
GRID    50630           6180.   1200.   30.     
GRID    50631           6180.   1200.   15.     
GRID    72313           6165.   1200.   180.    
GRID    72314           6015.   1200.   15.     
GRID    72315           6164.41 1200.   165.4588
GRID    72316           6029.5311200.   15.80358
GRID    72317           6043.8971200.   18.1306 
GRID    72318           6057.9421200.   21.94351
GRID    72319           6071.5191200.   27.18296
GRID    72320           6084.49 1200.   33.781  
GRID    72321           6096.7211200.   41.66823
GRID    72322           6108.1311200.   50.70133
GRID    72323           6118.6221200.   60.78716
GRID    72324           6128.1541200.   71.78422
GRID    72325           6136.68 1200.   83.5786 
GRID    72326           6144.0931200.   96.10227
GRID    72327           6150.4391200.   109.1989
GRID    72328           6155.6581200.   122.7839
GRID    72329           6159.7331200.   136.7548
GRID    72330           6162.6531200.   151.0121
GRID    72331           6153.5681200.   58.13178
GRID    72332           6121.8221200.   27.37471
GRID    72333           6148.7171200.   44.3185 
GRID    72334           6133.8781200.   30.38507
GRID    72335           6164.2731200.   104.5034
GRID    72336           6075.49 1200.   15.9285 
GRID    72337           6165.85 1200.   120.4246
GRID    72338           6093.9861200.   19.42911
GRID    72339           6160.4281200.   88.98135
GRID    72340           6147.8921200.   77.68674
GRID    72341           6139.5161200.   62.77402
GRID    72342           6131.1981200.   47.1932 
GRID    72343           6116.7341200.   40.18838
GRID    72344           6103.7971200.   33.05079
GRID    72345           6121.3461200.   13.62104
GRID    72346           6107.6151200.   14.09851
GRID    72347           6165.   1200.   15.     
GRID    72348           6166.2981200.   74.65051
GRID    72349           6166.7151200.   58.56164
GRID    72350           6150.   1200.   15.     
GRID    72351           6135.3271200.   14.6743 
GRID    72352           6164.4781200.   43.60722
GRID    72353           6165.   1200.   30.     
GRID    72354           6149.3581200.   30.07149
GRID    72355           6155.8631200.   70.90848
GRID    72356           6111.1  1200.   24.96382
$==============================================================================$
CTRIA3  101628  1       72336   72318   72319   
CTRIA3  101629  1       72337   72328   72329   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100097  1       33207   33208   33213   33212   
CQUAD4  100098  1       33208   33209   16605   33213   
CQUAD4  100099  1       33209   33210   33214   16605   
CQUAD4  100100  1       33210   33211   33215   33214   
CQUAD4  101583  1       72329   50623   50624   72337   
CQUAD4  101584  1       50628   50629   72352   72349   
CQUAD4  101585  1       72351   72350   33679   33630   
CQUAD4  101586  1       50631   72347   72353   50630   
CQUAD4  101587  1       72318   33382   33333   72317   
CQUAD4  101588  1       72319   72320   72338   72336   
CQUAD4  101589  1       33531   33481   72338   72346   
CQUAD4  101590  1       33432   72336   72338   33481   
CQUAD4  101591  1       33580   33531   72346   72345   
CQUAD4  101592  1       72353   72354   72333   72352   
CQUAD4  101593  1       72315   72313   16625   50621   
CQUAD4  101594  1       72339   72340   72325   72326   
CQUAD4  101595  1       50627   72348   72339   50626   
CQUAD4  101596  1       72325   72340   72341   72324   
CQUAD4  101597  1       72349   72352   72333   72331   
CQUAD4  101598  1       33630   33580   72345   72351   
CQUAD4  101599  1       50624   50625   72335   72337   
CQUAD4  101600  1       72342   72333   72354   72334   
CQUAD4  101601  1       72344   72321   72322   72343   
CQUAD4  101602  1       72316   33283   16605   72314   
CQUAD4  101603  1       72346   72356   72332   72345   
CQUAD4  101604  1       33729   33679   72350   72347   
CQUAD4  101605  1       50629   50630   72353   72352   
CQUAD4  101606  1       72328   72337   72335   72327   
CQUAD4  101607  1       72318   72336   33432   33382   
CQUAD4  101608  1       72331   72341   72340   72355   
CQUAD4  101609  1       33283   72316   72317   33333   
CQUAD4  101610  1       72342   72323   72324   72341   
CQUAD4  101611  1       72323   72342   72343   72322   
CQUAD4  101612  1       72342   72334   72332   72343   
CQUAD4  101613  1       72340   72339   72348   72355   
CQUAD4  101614  1       72345   72332   72334   72351   
CQUAD4  101615  1       33729   72347   50631   16615   
CQUAD4  101616  1       72329   72330   50622   50623   
CQUAD4  101617  1       72327   72335   72339   72326   
CQUAD4  101618  1       72321   72344   72338   72320   
CQUAD4  101619  1       72346   72338   72344   72356   
CQUAD4  101620  1       50621   50622   72330   72315   
CQUAD4  101621  1       50627   50628   72349   72348   
CQUAD4  101622  1       50625   50626   72339   72335   
CQUAD4  101623  1       72331   72355   72348   72349   
CQUAD4  101624  1       72353   72347   72350   72354   
CQUAD4  101625  1       72344   72343   72332   72356   
CQUAD4  101626  1       72342   72341   72331   72333   
CQUAD4  101627  1       72351   72334   72354   72350   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
