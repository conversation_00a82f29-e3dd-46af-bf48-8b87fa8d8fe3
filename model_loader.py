"""
BDF节点分类系统 - 模型加载器
============================
用于软件打包的模型加载和预测模块

作者：WZH
日期：2025-07-25
版本：1.0.0
"""

import os
import json
import joblib
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class BDFClassificationModel:
    """BDF节点分类模型加载器"""
    
    def __init__(self, model_dir: str = 'rf_classification_results'):
        """
        初始化模型加载器
        
        Args:
            model_dir: 模型文件目录
        """
        self.model_dir = model_dir
        self.models = {}
        self.config = {}
        self.feature_extractor = None
        self.dim_reducer = None
        self.class_weights = {}
        self.training_stats = {}
        self.sample_index = {}
        
        # 检查模型目录
        if not os.path.exists(model_dir):
            raise FileNotFoundError(f"模型目录不存在: {model_dir}")
        
        # 加载模型清单
        self._load_manifest()
        
        # 加载所有模型组件
        self._load_all_components()
    
    def _load_manifest(self):
        """加载模型清单"""
        manifest_path = os.path.join(self.model_dir, 'model_manifest.json')
        if os.path.exists(manifest_path):
            with open(manifest_path, 'r', encoding='utf-8') as f:
                self.manifest = json.load(f)
            print(f"✅ 模型清单加载成功")
        else:
            print("⚠️ 未找到模型清单文件，使用默认配置")
            self.manifest = {}
    
    def _load_all_components(self):
        """加载所有模型组件"""
        print("🔄 加载模型组件...")
        
        # 1. 加载分类模型
        try:
            self.models['type'] = joblib.load(os.path.join(self.model_dir, 'best_type_model.pkl'))
            self.models['dataset'] = joblib.load(os.path.join(self.model_dir, 'best_dataset_model.pkl'))
            print("  ✅ 分类模型加载成功")
        except Exception as e:
            raise RuntimeError(f"分类模型加载失败: {e}")
        
        # 2. 加载预处理组件
        try:
            self.dim_reducer = joblib.load(os.path.join(self.model_dir, 'dimensionality_reducer.pkl'))
            print("  ✅ 降维处理器加载成功")
        except Exception as e:
            raise RuntimeError(f"降维处理器加载失败: {e}")
        
        # 3. 加载特征提取器
        try:
            self.feature_extractor = joblib.load(os.path.join(self.model_dir, 'feature_extractor.pkl'))
            print("  ✅ 特征提取器加载成功")
        except Exception as e:
            print(f"  ⚠️ 特征提取器加载失败: {e}")
            print("  💡 将使用内置特征提取器")
        
        # 4. 加载配置信息
        try:
            self.config = joblib.load(os.path.join(self.model_dir, 'model_config.pkl'))
            print("  ✅ 模型配置加载成功")
        except Exception as e:
            print(f"  ⚠️ 模型配置加载失败: {e}")
        
        # 5. 加载类别权重
        try:
            self.class_weights = joblib.load(os.path.join(self.model_dir, 'class_weights.pkl'))
            print("  ✅ 类别权重加载成功")
        except Exception as e:
            print(f"  ⚠️ 类别权重加载失败: {e}")
        
        # 6. 加载训练统计
        try:
            self.training_stats = joblib.load(os.path.join(self.model_dir, 'training_stats.pkl'))
            print("  ✅ 训练统计加载成功")
        except Exception as e:
            print(f"  ⚠️ 训练统计加载失败: {e}")
        
        # 7. 加载样本索引
        try:
            self.sample_index = joblib.load(os.path.join(self.model_dir, 'sample_index.pkl'))
            print("  ✅ 样本索引加载成功")
        except Exception as e:
            print(f"  ⚠️ 样本索引加载失败: {e}")
        
        print("🎉 所有模型组件加载完成")
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        info = {
            'model_version': self.config.get('model_version', 'unknown'),
            'training_date': self.config.get('training_date', 'unknown'),
            'training_samples': self.config.get('training_samples', 0),
            'n_features': self.config.get('n_features', 0),
            'datasets_used': self.config.get('datasets_used', []),
            'type_names': self.config.get('type_names', {}),
            'dataset_names': self.config.get('dataset_names', {}),
            'training_accuracy': self.training_stats.get('training_accuracy', {})
        }
        return info
    
    def predict_from_features(self, features: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        从特征向量进行预测
        
        Args:
            features: 特征矩阵 (n_samples, n_features)
            
        Returns:
            type_predictions: 单元类型预测结果
            dataset_predictions: 数据集类型预测结果
        """
        # 数据预处理
        features_scaled = self.dim_reducer.scaler.transform(features)
        features_reduced = self.dim_reducer.reducers['pca'].transform(features_scaled)
        
        # 预测
        type_pred = self.models['type'].predict(features_reduced)
        dataset_pred = self.models['dataset'].predict(features_reduced)
        
        return type_pred, dataset_pred
    
    def predict_from_bdf_file(self, bdf_file_path: str, dataset_type: str = '自由边') -> Dict:
        """
        从BDF文件进行预测
        
        Args:
            bdf_file_path: BDF文件路径
            dataset_type: 数据集类型 ('自由边' 或 '趾端')
            
        Returns:
            预测结果字典
        """
        if self.feature_extractor is None:
            raise RuntimeError("特征提取器未加载，无法从BDF文件预测")
        
        # 解析BDF文件
        from bdf_rf_classifier_improved1 import BDFParser
        parser = BDFParser()
        nodes, elements = parser.parse_file(bdf_file_path)
        
        # 提取特征
        features, element_ids, feature_names = self.feature_extractor.extract_features_for_prediction(
            parser, dataset_type
        )
        
        if not features:
            raise ValueError("无法从BDF文件提取特征")
        
        # 预测
        features_array = np.array(features)
        type_pred, dataset_pred = self.predict_from_features(features_array)
        
        # 整理结果
        type_names = self.config.get('type_names', {0: '其他', 1: 'Read', 2: 'View'})
        dataset_names = self.config.get('dataset_names', {0: '自由边', 1: '趾端'})
        
        results = {
            'file_path': bdf_file_path,
            'total_elements': len(element_ids),
            'element_ids': element_ids,
            'type_predictions': type_pred.tolist(),
            'dataset_predictions': dataset_pred.tolist(),
            'type_names': [type_names[pred] for pred in type_pred],
            'dataset_names': [dataset_names[pred] for pred in dataset_pred],
            'type_distribution': {
                type_names[i]: int(np.sum(type_pred == i)) for i in type_names.keys()
            },
            'dataset_distribution': {
                dataset_names[i]: int(np.sum(dataset_pred == i)) for i in dataset_names.keys()
            }
        }
        
        return results
    
    def export_predictions_to_excel(self, predictions: Dict, output_path: str):
        """
        将预测结果导出到Excel文件
        
        Args:
            predictions: 预测结果字典
            output_path: 输出Excel文件路径
        """
        # 准备数据
        data = []
        for i, elem_id in enumerate(predictions['element_ids']):
            data.append({
                '单元ID': elem_id,
                '预测单元类型': predictions['type_names'][i],
                '预测数据集类型': predictions['dataset_names'][i],
                '单元类型编码': predictions['type_predictions'][i],
                '数据集类型编码': predictions['dataset_predictions'][i]
            })
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 保存到Excel
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='预测结果', index=False)
            
            # 添加统计汇总
            summary_data = []
            summary_data.append(['文件路径', predictions['file_path']])
            summary_data.append(['单元总数', predictions['total_elements']])
            summary_data.append(['', ''])
            summary_data.append(['单元类型分布', ''])
            for type_name, count in predictions['type_distribution'].items():
                percentage = count / predictions['total_elements'] * 100
                summary_data.append([type_name, f'{count}个 ({percentage:.1f}%)'])
            summary_data.append(['', ''])
            summary_data.append(['数据集类型分布', ''])
            for dataset_name, count in predictions['dataset_distribution'].items():
                percentage = count / predictions['total_elements'] * 100
                summary_data.append([dataset_name, f'{count}个 ({percentage:.1f}%)'])
            
            summary_df = pd.DataFrame(summary_data, columns=['项目', '值'])
            summary_df.to_excel(writer, sheet_name='统计汇总', index=False)
        
        print(f"✅ 预测结果已导出到: {output_path}")

def main():
    """示例使用方法"""
    try:
        # 1. 加载模型
        model = BDFClassificationModel('rf_classification_results')
        
        # 2. 查看模型信息
        info = model.get_model_info()
        print("\n📋 模型信息:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        # 3. 示例预测（需要提供实际的BDF文件路径）
        # bdf_file = "example.bdf"
        # if os.path.exists(bdf_file):
        #     results = model.predict_from_bdf_file(bdf_file)
        #     print(f"\n🎯 预测结果:")
        #     print(f"  单元总数: {results['total_elements']}")
        #     print(f"  类型分布: {results['type_distribution']}")
        #     print(f"  数据集分布: {results['dataset_distribution']}")
        #     
        #     # 导出结果
        #     model.export_predictions_to_excel(results, "prediction_results.xlsx")
        
        print("\n🎉 模型加载器测试完成")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
