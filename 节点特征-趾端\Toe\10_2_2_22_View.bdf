$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:05:57                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16602           -200.   4010.   0.      
GRID    16612           -200.   4060.   0.      
GRID    16622           -200.   4060.   70.     
GRID    33180           -180.   4000.   0.      
GRID    33181           -190.   4000.   0.      
GRID    33182           -200.   4000.   0.      
GRID    33183           -210.   4000.   0.      
GRID    33184           -220.   4000.   0.      
GRID    33185           -180.   4010.   0.      
GRID    33186           -190.   4010.   0.      
GRID    33187           -210.   4010.   0.      
GRID    33188           -220.   4010.   0.      
GRID    33268           -200.   4020.   0.      
GRID    33318           -200.   4030.   0.      
GRID    33367           -200.   4040.   0.      
GRID    33417           -200.   4050.   0.      
GRID    50352           -200.   4060.   60.     
GRID    50353           -200.   4060.   50.     
GRID    50354           -200.   4060.   40.     
GRID    50355           -200.   4060.   30.     
GRID    50356           -200.   4060.   20.     
GRID    50357           -200.   4060.   10.     
GRID    62645           -200.   4045.   70.     
GRID    62646           -200.   4010.   15.     
GRID    62647           -200.   4044.37959.82797
GRID    62648           -200.   4019.92217.3105 
GRID    62649           -200.   4028.27 23.15039
GRID    62650           -200.   4034.69931.05514
GRID    62651           -200.   4039.34440.12539
GRID    62652           -200.   4042.52249.80776
GRID    62653           -200.   4010.   7.5     
GRID    62654           -200.   4052.5  70.     
GRID    62655           -200.   4051.23859.71303
GRID    62656           -200.   4040.41 10.62908
GRID    62657           -200.   4050.5  9.399915
GRID    62658           -200.   4051.87918.74641
GRID    62659           -200.   4019.0028.534843
GRID    62660           -200.   4049.77349.28801
GRID    62661           -200.   4048.01537.44856
GRID    62662           -200.   4028.2  9.721301
GRID    62663           -200.   4040.31726.4227 
GRID    62664           -200.   4034.37317.28417
GRID    62665           -200.   4050.70128.17791
GRID    62666           -200.   4044.80718.48744
$==============================================================================$
CTRIA3  59259   1       62662   62659   62648   
CTRIA3  59260   1       62665   62663   62661   
CTRIA3  59261   1       62662   62664   62656   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  59238   1       62661   62660   50353   50354   
CQUAD4  59239   1       50352   62655   62654   16622   
CQUAD4  59240   1       62663   62665   62658   62666   
CQUAD4  59241   1       62651   62652   62660   62661   
CQUAD4  59242   1       62659   33268   16602   62653   
CQUAD4  59243   1       62647   62645   62654   62655   
CQUAD4  59244   1       62663   62666   62656   62664   
CQUAD4  59245   1       62658   50356   50357   62657   
CQUAD4  59246   1       62663   62650   62651   62661   
CQUAD4  59247   1       50356   62658   62665   50355   
CQUAD4  59248   1       62650   62663   62664   62649   
CQUAD4  59249   1       62658   62657   62656   62666   
CQUAD4  59250   1       62653   62646   62648   62659   
CQUAD4  59251   1       62662   62656   33367   33318   
CQUAD4  59252   1       62655   50352   50353   62660   
CQUAD4  59253   1       62652   62647   62655   62660   
CQUAD4  59254   1       62648   62649   62664   62662   
CQUAD4  59255   1       50354   50355   62665   62661   
CQUAD4  59256   1       50357   16612   33417   62657   
CQUAD4  59257   1       33318   33268   62659   62662   
CQUAD4  59258   1       33417   33367   62656   62657   
CQUAD4  100085  1       33180   33181   33186   33185   
CQUAD4  100086  1       33181   33182   16602   33186   
CQUAD4  100087  1       33182   33183   33187   16602   
CQUAD4  100088  1       33183   33184   33188   33187   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
