$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:03:38                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    11              -180.   -3.332-80.      
GRID    12              -190.   -3.517-80.      
GRID    13              -200.   -3.702-80.      
GRID    14              -210.   -3.887-80.      
GRID    15              -220.   -4.072-80.      
GRID    16              -180.   10.     0.      
GRID    17              -190.   10.     0.      
GRID    18              -200.   10.     0.      
GRID    19              -210.   10.     0.      
GRID    20              -220.   10.     0.      
$==============================================================================$
CQUAD4  100005  1       11      12      17      16      
CQUAD4  100006  1       12      13      18      17      
CQUAD4  100007  1       13      14      19      18      
CQUAD4  100008  1       14      15      20      19      
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
