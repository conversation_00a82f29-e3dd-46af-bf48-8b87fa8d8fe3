$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:01:14                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16601           4010.   0.      0.      
GRID    16611           4040.   0.      0.      
GRID    16621           4040.   0.      50.     
GRID    33171           4000.   -20.    0.      
GRID    33172           4000.   -10.    0.      
GRID    33173           4000.   0.      0.      
GRID    33174           4000.   10.     0.      
GRID    33175           4000.   20.     0.      
GRID    33176           4010.   -20.    0.      
GRID    33177           4010.   -10.    0.      
GRID    33178           4010.   10.     0.      
GRID    33179           4010.   20.     0.      
GRID    33263           4020.   0.      0.      
GRID    33313           4030.   0.      0.      
GRID    50299           4040.   0.      40.     
GRID    50300           4040.   0.      30.     
GRID    50301           4040.   0.      20.     
GRID    50302           4040.   0.      10.     
GRID    62633           4025.   0.      50.     
GRID    62634           4010.   0.      15.     
GRID    62635           4010.   0.      7.5     
GRID    62636           4032.5  0.      50.     
GRID    62637           4024.3440.      39.92526
GRID    62638           4018.2010.      20.81828
GRID    62639           4022.3  0.      30.03937
GRID    62640           4031.0440.      29.05648
GRID    62641           4029.6960.      19.52379
GRID    62642           4029.7460.      9.755176
GRID    62643           4031.7620.      39.31109
GRID    62644           4019.7640.      9.467024
$==============================================================================$
CQUAD4  59226   1       33313   33263   62644   62642   
CQUAD4  59227   1       50300   50301   62641   62640   
CQUAD4  59228   1       62638   62639   62640   62641   
CQUAD4  59229   1       50302   62642   62641   50301   
CQUAD4  59230   1       62637   62633   62636   62643   
CQUAD4  59231   1       62638   62644   62635   62634   
CQUAD4  59232   1       62636   16621   50299   62643   
CQUAD4  59233   1       62641   62642   62644   62638   
CQUAD4  59234   1       50302   16611   33313   62642   
CQUAD4  59235   1       62644   33263   16601   62635   
CQUAD4  59236   1       62639   62637   62643   62640   
CQUAD4  59237   1       62640   62643   50299   50300   
CQUAD4  100081  1       33171   33172   33177   33176   
CQUAD4  100082  1       33172   33173   16601   33177   
CQUAD4  100083  1       33173   33174   33178   16601   
CQUAD4  100084  1       33174   33175   33179   33178   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
