$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:39:31                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16572           -300.   3015.   0.      
GRID    16582           -300.   3090.   0.      
GRID    16592           -300.   3090.   105.    
GRID    16640           -270.   3000.   0.      
GRID    16641           -285.   3000.   0.      
GRID    16642           -300.   3000.   0.      
GRID    16643           -315.   3000.   0.      
GRID    16644           -330.   3000.   0.      
GRID    16645           -270.   3015.   0.      
GRID    16646           -285.   3015.   0.      
GRID    16647           -315.   3015.   0.      
GRID    16648           -330.   3015.   0.      
GRID    16728           -300.   3030.   0.      
GRID    16778           -300.   3045.   0.      
GRID    16827           -300.   3060.   0.      
GRID    16877           -300.   3075.   0.      
GRID    49749           -300.   3090.   90.     
GRID    49750           -300.   3090.   75.     
GRID    49751           -300.   3090.   60.     
GRID    49752           -300.   3090.   45.     
GRID    49753           -300.   3090.   30.     
GRID    49754           -300.   3090.   15.     
GRID    71326           -300.   3075.   105.    
GRID    71327           -300.   3015.   15.     
GRID    71328           -300.   3074.15990.18579
GRID    71329           -300.   3029.57717.75945
GRID    71330           -300.   3042.47125.09506
GRID    71331           -300.   3053.05935.48817
GRID    71332           -300.   3061.30347.82362
GRID    71333           -300.   3067.42461.34004
GRID    71334           -300.   3071.66275.55959
GRID    71335           -300.   3077.36659.02478
GRID    71336           -300.   3072.48432.50837
GRID    71337           -300.   3058.43418.00521
GRID    71338           -300.   3073.17416.89507
GRID    71339           -300.   3075.20245.69008
GRID    71340           -300.   3045.69114.46414
$==============================================================================$
CTRIA3  100833  1       71335   71333   71334   
CTRIA3  100834  1       71340   71329   71330   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  100045  1       16640   16641   16646   16645   
CQUAD4  100046  1       16641   16642   16572   16646   
CQUAD4  100047  1       16642   16643   16647   16572   
CQUAD4  100048  1       16643   16644   16648   16647   
CQUAD4  100818  1       49751   71335   71334   49750   
CQUAD4  100819  1       49752   49753   71336   71339   
CQUAD4  100820  1       71340   71330   71331   71337   
CQUAD4  100821  1       71331   71332   71339   71336   
CQUAD4  100822  1       16778   16728   71329   71340   
CQUAD4  100823  1       49749   49750   71334   71328   
CQUAD4  100824  1       71333   71335   71339   71332   
CQUAD4  100825  1       71337   71338   16877   16827   
CQUAD4  100826  1       49751   49752   71339   71335   
CQUAD4  100827  1       71337   16827   16778   71340   
CQUAD4  100828  1       71328   71326   16592   49749   
CQUAD4  100829  1       71338   71337   71331   71336   
CQUAD4  100830  1       49754   16582   16877   71338   
CQUAD4  100831  1       71336   49753   49754   71338   
CQUAD4  100832  1       71329   16728   16572   71327   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
