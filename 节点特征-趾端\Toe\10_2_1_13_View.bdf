$==============================================================================$
$                                                                              $
$ FEGate 5.01.20 - Nastran Input Data Export                                   $
$ Date: 2021-06-09 15:04:48                                                    $
$                                                                              $
$==============================================================================$
$$BEGIN BULK
$==============================================================================$
$ *** MATERIAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.mat 1        "Mat"
$svd.yield 1        235.0 1.0
MAT1    1       206000. 80000.  0.3     7.8-9                           
$
$==============================================================================$
$ *** PHYSICAL PROPERTY
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$svd.part 1        S05 "ReadAndToe"
PSHELL  1       1       1.      1       1.      1       0.8333330.      
$
$==============================================================================$
$ *** NODE
$GRID   ID      CP      X1      X2      X3      CD      PS      SEID    
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
GRID    16573           2010.   400.    0.      
GRID    16583           2080.   400.    0.      
GRID    16593           2080.   400.    90.     
GRID    16649           2000.   380.    0.      
GRID    16650           2000.   390.    0.      
GRID    16651           2000.   400.    0.      
GRID    16652           2000.   410.    0.      
GRID    16653           2000.   420.    0.      
GRID    16654           2010.   380.    0.      
GRID    16655           2010.   390.    0.      
GRID    16656           2010.   410.    0.      
GRID    16657           2010.   420.    0.      
GRID    16733           2020.   400.    0.      
GRID    16783           2030.   400.    0.      
GRID    16832           2040.   400.    0.      
GRID    16882           2050.   400.    0.      
GRID    16931           2060.   400.    0.      
GRID    16981           2070.   400.    0.      
GRID    49775           2080.   400.    80.     
GRID    49776           2080.   400.    70.     
GRID    49777           2080.   400.    60.     
GRID    49778           2080.   400.    50.     
GRID    49779           2080.   400.    40.     
GRID    49780           2080.   400.    30.     
GRID    49781           2080.   400.    20.     
GRID    49782           2080.   400.    10.     
GRID    61623           2065.   400.    90.     
GRID    61624           2010.   400.    15.     
GRID    61625           2010.   400.    7.5     
GRID    61626           2072.5  400.    90.     
GRID    61627           2064.433400.    79.76173
GRID    61628           2020.167400.    16.32685
GRID    61629           2029.724400.    20.041  
GRID    61630           2038.265400.    25.71497
GRID    61631           2045.61 400.    32.86935
GRID    61632           2051.685400.    41.12972
GRID    61633           2056.538400.    50.16246
GRID    61634           2060.274400.    59.71137
GRID    61635           2062.901400.    69.62282
GRID    61636           2071.594400.    28.59981
GRID    61637           2069.239400.    19.40459
GRID    61638           2059.63 400.    10.15585
GRID    61639           2058.551400.    21.0608 
GRID    61640           2068.919400.    59.35699
GRID    61641           2070.886400.    69.4375 
GRID    61642           2032.628400.    11.95781
GRID    61643           2021.152400.    9.879757
GRID    61644           2066.897400.    47.78049
GRID    61645           2049.101400.    11.47578
GRID    61646           2059.627400.    37.31957
GRID    61647           2051.983400.    27.58093
GRID    61648           2043.098400.    18.78671
GRID    61649           2070.192400.    38.20431
GRID    61650           2071.94 400.    79.70841
GRID    61651           2070.   400.    10.     
GRID    61652           2063.503400.    28.81465
GRID    61653           2040.561400.    7.296929
$==============================================================================$
CTRIA3  58491   1       61646   61644   61649   
CTRIA3  58492   1       61639   61638   61645   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
CQUAD4  58460   1       61637   61651   61638   61639   
CQUAD4  58461   1       61627   61623   61626   61650   
CQUAD4  58462   1       61645   61648   61647   61639   
CQUAD4  58463   1       61650   49775   49776   61641   
CQUAD4  58464   1       61630   61648   61642   61629   
CQUAD4  58465   1       61646   61649   61636   61652   
CQUAD4  58466   1       61631   61647   61648   61630   
CQUAD4  58467   1       49777   49778   61644   61640   
CQUAD4  58468   1       49779   61649   61644   49778   
CQUAD4  58469   1       61644   61646   61632   61633   
CQUAD4  58470   1       16981   61651   49782   16583   
CQUAD4  58471   1       61642   16783   16733   61643   
CQUAD4  58472   1       16832   16783   61642   61653   
CQUAD4  58473   1       61643   16733   16573   61625   
CQUAD4  58474   1       61626   16593   49775   61650   
CQUAD4  58475   1       61636   61637   61639   61652   
CQUAD4  58476   1       61633   61634   61640   61644   
CQUAD4  58477   1       61641   61640   61634   61635   
CQUAD4  58478   1       61645   16882   16832   61653   
CQUAD4  58479   1       49776   49777   61640   61641   
CQUAD4  58480   1       49779   49780   61636   61649   
CQUAD4  58481   1       61632   61646   61647   61631   
CQUAD4  58482   1       61653   61642   61648   61645   
CQUAD4  58483   1       61647   61646   61652   61639   
CQUAD4  58484   1       61629   61642   61643   61628   
CQUAD4  58485   1       49781   61637   61636   49780   
CQUAD4  58486   1       16981   16931   61638   61651   
CQUAD4  58487   1       16882   61645   61638   16931   
CQUAD4  58488   1       49782   61651   61637   49781   
CQUAD4  58489   1       61628   61643   61625   61624   
CQUAD4  58490   1       61635   61627   61650   61641   
CQUAD4  100049  1       16649   16650   16655   16654   
CQUAD4  100050  1       16650   16651   16573   16655   
CQUAD4  100051  1       16651   16652   16656   16573   
CQUAD4  100052  1       16652   16653   16657   16656   
$1..... 2...... 3...... 4...... 5...... 6...... 7...... 8...... 9...... 
$$ENDDATA
